EMS 光伏储能并网电解槽柔性制氢，功率分配按如下要求输出结果：

# 1. 输入端数据
## 1.1 光伏设备数据
### 参数：
- 装机容量范围(MW): M~N 之间 [用户可输入M，N]
- 设备年衰减率：首年0.01，之后每年0.0035的衰减率 [用户可输入]
- 出力曲线：8760小时标幺值。测试数据见 pv.txt
- EPC投资：默认值 2.8元/W [用户可输入]
- 运维成本：默认值 0.05元/W/年 [用户可输入]

## 1.2 风机
### 参数：
- 装机容量范围(MW)：M~N 之间 [用户可输入M，N]
- 出力曲线：8760小时标幺值. 测试数据见 wind.txt
- EPC投资：3.2元/W [用户可输入]
- 运维成本：0.6元/W/年 [用户可输入]


## 1.3 电网相关条件
### 参数：
- 年最大下网比例：10%，即 下网电量/新能源预测发电量 的值不超所 10% [用户可输入]
- 年最大上网比例: 20%，即 上网电量/新能源预测发电量 的值不超所 20% [用户可输入]
- 网购电价均价：0.45元/kwh [用户可输入]
- 绿电上网价格：0.332元/kwh  [用户可输入]

### 注意：
约定电网下网功率值为正，上网功率值为负

## 1.4 储能相关条件
### 参数：
- 装机容量范围(MWh)：M~N 之间 [用户可输入M，N]
- EPC投资: 0.7元/Wh [用户可输入]
- 运维成本：0.04元/Wh/年 [用户可输入]
- 储能设备参数 
  - 充电效率: 默认值 0.95 [用户可输入]
  - 放电效率：默认值 0.95 [用户可输入]
  - 充放电倍率：默认值 0.5 [用户可输入]
  - 初始SOC：默认值 0.5 [用户可输入]
  - SOC 下限：默认值 0.1 [用户可输入]
  - SOC 上限：默认值 0.9 [用户可输入]

### 注意
储能放电，数值为正，储能充电，数值为负

## 1.5 电解槽相关条件
### 参数：
- 装机容量范围(WW)：M~N 之间 [用户可输入M，N]
- 用水价格：默认值 4.38元/吨 [用户可输入]
- 制氢耗水量：默认值 1.4L/Nm³ [用户可输入]
- 运维比例：2% [用户可输入]
- 电解槽设备 [常量值，之后可扩展到10+种类型以上]
  - Hi1_PLus_1000
    - 容量：1000Nm³/h
    - 电耗：4.3kWh/Nm³
    - 额定功率：5MW
    - 最低负载率：0.3
    - 最高负载率：1.1
    - 辅助系统能耗：0.3kWh/Nm³
    - EPC价格：8500000 元/套
  - G_2000
    - 容量：2000Nm³/h
    - 电耗：4.3kWh/Nm³
    - 额定功率：10MW
    - 最低负载率：0.3
    - 最高负载率：1.1
    - 辅助系统能耗：0.3kWh/Nm³
    - EPC价格：12000000元/套
  - G_3000
    - 容量：3000Nm³/h
    - 电耗：4.3kWh/Nm³
    - 额定功率：15MW
    - 最低负载率：0.3
    - 最高负载率：1.1
    - 辅助系统能耗：0.3kWh/Nm³
    - EPC价格：16000000元/套
  - P_200
    - 容量：200Nm³/h
    - 电耗：4.3kWh/Nm³
    - 额定功率：1MW
    - 最低负载率：0
    - 最高负载率：1.1
    - 辅助系统能耗：0.3kWh/Nm³
    - EPC价格：1700000元/套
  
### 注意
- 电解槽设备可多选，最多选中3种类型，选后后参与计算

## 1.7 其他参数
- 项目周期：25  [用户可输入]
- 目标年产氢量(吨)：[用户可输入]
- 贷款年限：15 [用户可输入]
- 贷款比例：0.7 [用户可输入]
- 贷款利率：0.031 [用户可输入]
- 设备折现率：0.06 [用户可输入]

## 1.8 求解目标
- 目标：LCOH最低。
- LCOH=(投资成本+运维成本的折现值累计)/产氢量的折现值累计
  - 投资成本=光伏投资成本 + 风机投资成本 + 储能投资成本 + 电解槽投资成本(不同容量 * 不同的系统单价)
  - 第二年(第一年为建设期)运维成本 = 光伏运维成本  + 风机运维成本 + 储能运维成本 + 电解槽运维成本(根据运维比例) + 电解槽水费 + 下网电费 - 上网收益 + 贷款本金及利息(等额本息)
  - 第三年及之后运维成本 = 根据第二年的折现
  - 第二年产氢量 = 每小时电解槽消纳的功率 *1小时 / 对应电解槽的电耗，然后累计8760小时

## 1.9 一些重要的计算公式
- 光伏当前小时功率 = 光伏容量 * 当前小时的标幺值
- 风机当前小时功率 = 风机容量 * 当前小时的标幺值
- 当前小时的产氢量 = 功率值 * 1小时 / 电解槽电耗。注意电解槽只在最小功率以上产氢。


# 2.分配策略
## 2.1 绿电电力充足时(绿电功率 >= 所有电解槽最高负荷的累加值)的功率分配优先级：
第一优先级：电解槽满功率(最高负载率：1.1，表示满功率可能为110%)用电
第二优先级：储能充电(满足SOC等限制条件)
第三优先级：电网上网（限制比例等限制条件）
第四优先级：弃掉绿电

## 2.2 绿电电力不足(所有电解槽最低负荷累加值 <= 绿电功能 < 有电解槽最高负荷的累加值)时的补充顺序：
电解槽消纳绿电，储能和电网不动

## 2.3 绿电电力较少(绿电功率 < 所有电解槽最低负荷累加值)时的补充顺序：
第一优先级：储能放电(满足SOC等限制条件限)，来支撑所有电解槽最大功率运行 >= 最低负荷
第二优先级：电网下电（限制比例等限制条件），来支撑所有电解槽最大功率运行 >= 最低负荷
第三优先级：不足以支撑所有电解槽最小功率运行的话，保证最小数量的电解槽停机，开机的电解槽等比例均分功率



# 3. 输出端数据
## 3.1 输出内容
必须严格考虑如上限制条件，基于粒子群算法，计算并输出如下数据：
- 电量统计。包含风机，光伏，储能，电网这几项发电量(MWh)数据及占比
- 年上网电量(MWh)，上网电量比例；年下网电量(MWh)，下网电量比例；
- 绿电的弃电率(弃电量/绿电发电量)
- 8760小时的功率曲线数据。包含新能源(风机加光伏相加的一条曲线)功率，储能功率，储能SOC，电网功率，多台电解槽总功率在8760小时下的出力功率曲线，电解槽单台功率曲线也有，可以堆叠累加，其他正常，风光储氢整体放在一个图。
- 要求，电网下网数值为正，上网数值为负；储能放电，数值为正，储能充电，数值为负；
- 以上数据结果前端：以 vue3+vite+antd vue组件的前端技术栈输出展示，曲线使用 echarts：
- 后端基于 python，有算法计算模块方法，然后生成一个简单的 server，可以调用计算方法，生成数据已 json 格式保存到 output 里，名称用1,2.. 递增方式命名，然后前端可以通过接口获取及渲染 json 里的数据

## 3.2 注意点
运行结果的曲线图，当前时刻的功率是基于上一时刻的数据计算的，有时序关联性。

