"""
FastAPI主程序
"""

import json
import os
import uuid
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from models.parameters import OptimizationParameters, OptimizationResult
from algorithms.pso import DynamicPSO
from config.electrolyzer_specs import get_electrolyzer_types

app = FastAPI(
    title="风光储氢容量优化系统",
    description="基于PSO算法的风光储氢容量测算系统",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 确保输出目录存在
output_dir = "../output"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "风光储氢容量优化系统 API",
        "version": "1.0.0",
        "status": "运行中"
    }

@app.get("/api/electrolyzer-types")
async def get_available_electrolyzer_types():
    """获取可用的电解槽类型"""
    return {
        "electrolyzer_types": get_electrolyzer_types(),
        "max_selection": 3
    }

@app.post("/api/optimize")
async def start_optimization(params: OptimizationParameters):
    """
    启动PSO优化计算
    
    Args:
        params: 优化参数
    
    Returns:
        dict: 包含结果ID和状态
    """
    try:
        # 验证电解槽选择
        if not params.selected_electrolyzer_types:
            raise HTTPException(status_code=400, detail="必须选择至少一种电解槽类型")
        
        if len(params.selected_electrolyzer_types) > 3:
            raise HTTPException(status_code=400, detail="最多只能选择3种电解槽类型")
        
        # 生成结果ID
        result_id = str(len([f for f in os.listdir(output_dir) if f.endswith('.json')]) + 1)
        
        print(f"开始优化计算，结果ID: {result_id}")
        print(f"选择的电解槽类型: {params.selected_electrolyzer_types}")
        
        # 创建PSO优化器
        pso = DynamicPSO(params.selected_electrolyzer_types, params)
        
        # 执行优化
        optimization_result = pso.optimize()
        
        # 添加计算信息
        calculation_info = {
            "result_id": result_id,
            "calculation_time": datetime.now().isoformat(),
            "parameters": params.model_dump(),
            "selected_electrolyzer_types": params.selected_electrolyzer_types
        }
        
        # 合并结果
        final_result = {**optimization_result, **calculation_info}
        
        # 保存结果到文件
        result_file = os.path.join(output_dir, f"{result_id}.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"优化结果已保存到: {result_file}")
        
        return {
            "result_id": result_id,
            "status": "success",
            "message": "优化计算完成",
            "lcoh": optimization_result.get('optimal_lcoh', 0),
            "calculation_time": optimization_result.get('calculation_time', 0)
        }
        
    except Exception as e:
        print(f"优化计算失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"优化计算失败: {str(e)}")

@app.get("/api/results/{result_id}")
async def get_optimization_result(result_id: str):
    """
    获取优化计算结果
    
    Args:
        result_id: 结果ID
    
    Returns:
        dict: 优化结果
    """
    try:
        result_file = os.path.join(output_dir, f"{result_id}.json")
        
        if not os.path.exists(result_file):
            raise HTTPException(status_code=404, detail=f"结果文件不存在: {result_id}")
        
        with open(result_file, 'r', encoding='utf-8') as f:
            result = json.load(f)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取结果失败: {str(e)}")

@app.get("/api/results")
async def list_optimization_results():
    """
    获取所有优化结果列表
    
    Returns:
        list: 结果列表
    """
    try:
        results = []
        
        for filename in os.listdir(output_dir):
            if filename.endswith('.json'):
                result_id = filename[:-5]  # 去掉.json后缀
                result_file = os.path.join(output_dir, filename)
                
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                    
                    # 提取关键信息
                    summary = {
                        "result_id": result_id,
                        "calculation_time": result.get('calculation_time', ''),
                        "lcoh": result.get('optimal_lcoh', 0),
                        "pv_capacity": result.get('optimal_pv_capacity', 0),
                        "wind_capacity": result.get('optimal_wind_capacity', 0),
                        "storage_capacity": result.get('optimal_storage_capacity', 0),
                        "h2_production": result.get('annual_h2_production', 0),
                        "selected_electrolyzer_types": result.get('selected_electrolyzer_types', [])
                    }
                    
                    results.append(summary)
                    
                except Exception as e:
                    print(f"读取结果文件失败 {filename}: {e}")
                    continue
        
        # 按result_id排序
        results.sort(key=lambda x: int(x['result_id']))
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取结果列表失败: {str(e)}")

@app.delete("/api/results/{result_id}")
async def delete_optimization_result(result_id: str):
    """
    删除优化结果
    
    Args:
        result_id: 结果ID
    
    Returns:
        dict: 删除状态
    """
    try:
        result_file = os.path.join(output_dir, f"{result_id}.json")
        
        if not os.path.exists(result_file):
            raise HTTPException(status_code=404, detail=f"结果文件不存在: {result_id}")
        
        os.remove(result_file)
        
        return {
            "status": "success",
            "message": f"结果 {result_id} 已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除结果失败: {str(e)}")

@app.get("/api/parameters/default")
async def get_default_parameters():
    """获取默认参数"""
    default_params = OptimizationParameters()
    return default_params.model_dump()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001) 