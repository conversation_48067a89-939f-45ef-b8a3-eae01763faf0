import axios from 'axios'

const API_BASE_URL = ''

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5分钟超时
})

export interface OptimizationResult {
  result_id: string
  optimal_pv_capacity: number
  optimal_wind_capacity: number
  optimal_storage_capacity: number
  optimal_electrolyzer_config: Array<{
    type: string
    count: number
    rated_power: number
    total_capacity: number
  }>
  optimal_lcoh: number
  annual_h2_production: number
  annual_pv_generation: number
  annual_wind_generation: number
  annual_grid_up: number
  annual_grid_down: number
  annual_curtailed: number
  grid_up_ratio: number
  grid_down_ratio: number
  curtailment_ratio: number
  total_capex: number
  total_opex: number
  annual_income: number
  hourly_data: Array<{
    hour: number
    pv_power: number
    wind_power: number
    renewable_power: number
    storage_power: number
    storage_soc: number
    grid_power: number
    electrolyzer_total_power: number
    h2_production: number
    curtailed_power: number
  }>
  calculation_time: string
  optimization_iterations: number
  convergence_achieved: boolean
}

export const resultsApi = {
  // 获取优化结果
  async getResult(resultId: string): Promise<OptimizationResult> {
    const response = await api.get(`/api/results/${resultId}`)
    return response.data
  },

  // 获取结果列表
  async getResultsList() {
    const response = await api.get('/api/results')
    return response.data
  },

  // 删除结果
  async deleteResult(resultId: string) {
    const response = await api.delete(`/api/results/${resultId}`)
    return response.data
  },

  // 获取默认参数
  async getDefaultParameters() {
    const response = await api.get('/api/parameters/default')
    return response.data
  },

  // 启动优化计算
  async startOptimization(params: any) {
    const response = await api.post('/api/optimize', params)
    return response.data
  }
} 