# 风光储氢容量优化系统

基于PSO算法的风光储氢容量测算系统，实现LCOH（制氢成本）最低的目标优化。

## 系统特性

- **智能优化**：基于粒子群算法(PSO)的动态维度优化
- **全面约束**：考虑电网、储能、电解槽等多重约束条件
- **精确仿真**：8760小时逐时仿真，EMS能量管理策略
- **经济计算**：完整的LCOH计算模型，包含投资、运维、折现等
- **灵活配置**：所有参数用户可配置，支持1-3种电解槽类型选择
- **可视化展示**：丰富的图表展示优化结果和功率曲线

## 技术架构

```
前端: Vue 3 + TypeScript + Ant Design Vue + ECharts
后端: Python + FastAPI + NumPy + Pandas + PSO算法
数据: JSON文件存储计算结果
```

## 系统要求

- Python 3.8+
- Node.js 16+
- npm 或 yarn

## 快速开始

### 1. 安装后端依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 启动后端服务

```bash
cd backend
python main.py
```

后端服务将在 http://localhost:8000 启动

### 3. 安装前端依赖

```bash
cd frontend
npm install
```

### 4. 启动前端服务

```bash
cd frontend
npm run dev
```

前端服务将在 http://localhost:3000 启动

### 5. 访问系统

打开浏览器访问：http://localhost:3000/results/1

## API 接口

### 后端接口

- `GET /` - 系统状态
- `GET /api/electrolyzer-types` - 获取电解槽类型
- `POST /api/optimize` - 启动PSO优化计算
- `GET /api/results/{id}` - 获取计算结果
- `GET /api/results` - 获取结果列表
- `DELETE /api/results/{id}` - 删除结果
- `GET /api/parameters/default` - 获取默认参数

### 使用示例

```python
# 发起优化计算
import requests

params = {
    "selected_electrolyzer_types": ["Hi1_PLus_1000", "G_2000"],
    "target_h2_production": 1000,
    "pv_capacity_min": 10,
    "pv_capacity_max": 200,
    # ... 其他参数
}

response = requests.post("http://localhost:8000/api/optimize", json=params)
result = response.json()
print(f"结果ID: {result['result_id']}")
```

## 配置说明

### 主要输入参数

#### 光伏设备参数
- 装机容量范围：10-200 MW
- 首年衰减率：1%
- 年衰减率：0.35%
- EPC投资：2.8元/W
- 运维成本：0.05元/W/年

#### 风机参数
- 装机容量范围：10-200 MW
- EPC投资：3.2元/W
- 运维成本：0.6元/W/年

#### 储能参数
- 装机容量范围：10-500 MWh
- 充放电效率：95%/95%
- 充放电倍率：0.5
- SOC范围：10%-90%
- EPC投资：0.7元/Wh

#### 电解槽参数
- 总容量范围：20-200 MW
- 可选类型：Hi1_PLus_1000, G_2000, G_3000, P_200
- 最多选择3种类型

#### PSO算法参数
- 粒子群规模：40
- 最大迭代次数：150
- 惯性权重：0.9 → 0.4
- 学习因子：c1=2.0, c2=2.0

## 算法说明

### PSO优化流程

1. **动态粒子构建**：根据选择的电解槽类型构建4-6维粒子
2. **约束检查**：电解槽总容量、电网比例、储能SOC等约束
3. **8760小时仿真**：EMS能量管理策略逐时计算
4. **LCOH计算**：考虑投资、运维、折现、售电收入
5. **惩罚函数**：产氢量偏差惩罚项
6. **全局优化**：寻找最低LCOH方案

### EMS策略

**绿电充足时**（绿电 ≥ 电解槽最高负荷）：
1. 电解槽满功率用电
2. 储能充电
3. 电网上网
4. 弃掉绿电

**绿电不足时**（最低负荷 ≤ 绿电 < 最高负荷）：
- 电解槽消纳绿电，按比例分配

**绿电较少时**（绿电 < 最低负荷）：
1. 储能放电补充
2. 电网下网补充
3. 小容量电解槽优先停机

## 结果展示

系统提供丰富的可视化结果：

- **关键指标**：LCOH、年产氢量、总投资、计算时间
- **装机配置**：光伏、风机、储能、电解槽容量
- **电量统计**：各类发电量和比例的饼图
- **功率曲线**：8760小时新能源、储能、电网、电解槽功率曲线
- **SOC变化**：储能荷电状态动态曲线
- **产氢量**：逐时产氢量柱状图

## 项目结构

```
cap-v7.7/
├── backend/                    # Python后端
│   ├── main.py                # FastAPI主程序
│   ├── models/                # 数据模型
│   │   └── parameters.py      # 参数验证模型
│   ├── algorithms/            # 核心算法
│   │   ├── pso.py            # PSO优化算法
│   │   ├── ems.py            # EMS能量管理
│   │   └── economics.py      # 经济性计算
│   ├── config/               # 配置文件
│   │   └── electrolyzer_specs.py # 电解槽规格
│   ├── utils/                # 工具函数
│   │   └── data_loader.py    # 数据加载
│   └── requirements.txt      # Python依赖
├── frontend/                  # Vue3前端
│   ├── src/
│   │   ├── views/            # 页面组件
│   │   │   └── ResultDetail.vue # 结果详情页
│   │   ├── api/              # API调用
│   │   │   └── results.ts    # 结果API
│   │   ├── router/           # 路由配置
│   │   │   └── index.ts      # 路由定义
│   │   ├── App.vue          # 根组件
│   │   └── main.ts          # 入口文件
│   ├── package.json         # Node依赖
│   └── vite.config.ts       # Vite配置
├── output/                   # 计算结果存储
├── pv.txt                   # 光伏8760小时出力曲线
├── wind.txt                 # 风机8760小时出力曲线
└── README.md               # 项目说明
```

## 开发说明

### 添加新的电解槽类型

1. 在 `backend/config/electrolyzer_specs.py` 中添加新规格
2. 更新前端的电解槽类型枚举

### 修改优化算法

主要算法逻辑在 `backend/algorithms/` 目录：
- `pso.py` - PSO算法实现
- `ems.py` - EMS能量管理策略  
- `economics.py` - 经济性计算模型

### 自定义前端展示

结果展示页面在 `frontend/src/views/ResultDetail.vue`，可根据需要修改图表样式和数据展示方式。

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或 Pull Request。 