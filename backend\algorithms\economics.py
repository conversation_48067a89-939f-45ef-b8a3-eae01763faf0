"""
经济性计算模块
"""

import numpy as np
from config.electrolyzer_specs import ELECTROLYZER_SPECS
from models.parameters import OptimizationParameters

def calculate_total_capex(particle, selected_types, params: OptimizationParameters):
    """
    计算总投资成本 (CAPEX)
    
    Args:
        particle: 粒子 [pv_capacity, wind_capacity, storage_capacity, elec1_count, ...]
        selected_types: 选择的电解槽类型列表
        params: 优化参数
    
    Returns:
        float: 总投资成本 (元)
    """
    pv_capacity, wind_capacity, storage_capacity = particle[:3]
    elec_counts = particle[3:]
    
    # 光伏投资
    pv_capex = pv_capacity * 1000 * 1000 * params.pv_epc_cost  # MW转W (1MW = 1,000,000W)
    
    # 风机投资
    wind_capex = wind_capacity * 1000 * 1000 * params.wind_epc_cost  # MW转W (1MW = 1,000,000W)
    
    # 储能投资
    storage_capex = storage_capacity * 1000 * 1000 * params.storage_epc_cost  # MWh转Wh
    
    # 电解槽投资
    elec_capex = 0
    for i, elec_type in enumerate(selected_types):
        elec_spec = ELECTROLYZER_SPECS[elec_type]
        elec_capex += int(elec_counts[i]) * elec_spec['epc_price']
    
    total_capex = pv_capex + wind_capex + storage_capex + elec_capex
    return total_capex

def calculate_annual_opex(particle, selected_types, params: OptimizationParameters):
    """
    计算年运维成本 (OPEX)
    
    Args:
        particle: 粒子
        selected_types: 选择的电解槽类型列表  
        params: 优化参数
    
    Returns:
        float: 年运维成本 (元/年)
    """
    pv_capacity, wind_capacity, storage_capacity = particle[:3]
    elec_counts = particle[3:]
    
    # 光伏运维
    pv_opex = pv_capacity * 1000 * 1000 * params.pv_om_cost  # MW转W (1MW = 1,000,000W)
    
    # 风机运维
    wind_opex = wind_capacity * 1000 * 1000 * params.wind_om_cost  # MW转W (1MW = 1,000,000W)
    
    # 储能运维
    storage_opex = storage_capacity * 1000 * 1000 * params.storage_om_cost
    
    # 电解槽运维 (按投资比例计算)
    elec_opex = 0
    for i, elec_type in enumerate(selected_types):
        elec_spec = ELECTROLYZER_SPECS[elec_type]
        elec_investment = int(elec_counts[i]) * elec_spec['epc_price']
        elec_opex += elec_investment * params.om_ratio
    
    total_opex = pv_opex + wind_opex + storage_opex + elec_opex
    return total_opex

def calculate_annuity_payment(principal, rate, years):
    """
    计算等额本息还款金额
    
    Args:
        principal: 本金
        rate: 年利率
        years: 年限
    
    Returns:
        float: 年还款金额
    """
    if rate == 0:
        return principal / years
    else:
        return principal * rate * (1 + rate) ** years / ((1 + rate) ** years - 1)

def calculate_lcoh(particle, selected_types, annual_stats, params: OptimizationParameters):
    """
    计算平准化制氢成本 (LCOH)
    
    Args:
        particle: 优化粒子
        selected_types: 选择的电解槽类型
        annual_stats: 年度统计数据
        params: 优化参数
    
    Returns:
        float: LCOH (元/kg)
    """
    
    # 1. 投资成本 (CAPEX)
    capex = calculate_total_capex(particle, selected_types, params)
    
    # 2. 年运维成本 (OPEX)
    first_year_opex = calculate_annual_opex(particle, selected_types, params)
    
    # 2.1 制氢水费 (重要遗漏项)
    first_year_h2 = annual_stats['total_h2_production']  # Nm³
    water_cost = first_year_h2 * (params.water_consumption / 1000) * params.water_price  # L转m³
    first_year_opex += water_cost
    
    # 2.2 电网购电成本
    grid_purchase_cost = annual_stats['total_grid_down'] * params.grid_purchase_price * 1000  # MWh转kWh
    first_year_opex += grid_purchase_cost
    
    # 3. 折现的运维成本 (25年)
    total_discounted_opex = 0
    for year in range(1, params.project_years + 1):
        yearly_opex = first_year_opex
        discounted_opex = yearly_opex / ((1 + params.discount_rate) ** (year - 1))
        total_discounted_opex += discounted_opex
    
    # 4. 折现的总产氢量 (25年，考虑设备衰减)
    total_discounted_h2 = 0
    first_year_h2 = annual_stats['total_h2_production']
    
    for year in range(1, params.project_years + 1):
        # 光伏衰减影响产氢量
        if year == 1:
            degradation_factor = 1 - params.pv_first_year_degradation
        else:
            degradation_factor = (1 - params.pv_first_year_degradation) * \
                               ((1 - params.pv_annual_degradation) ** (year - 2))
        
        yearly_h2 = first_year_h2 * degradation_factor
        discounted_h2 = yearly_h2 / ((1 + params.discount_rate) ** (year - 1))
        total_discounted_h2 += discounted_h2
    
    # 5. 折现的售电收入 (绿电上网收入)
    total_discounted_income = 0
    first_year_income = annual_stats['total_grid_up'] * params.green_electricity_price
    
    for year in range(1, params.project_years + 1):
        # 光伏衰减影响售电收入
        if year == 1:
            degradation_factor = 1 - params.pv_first_year_degradation
        else:
            degradation_factor = (1 - params.pv_first_year_degradation) * \
                               ((1 - params.pv_annual_degradation) ** (year - 2))
        
        yearly_income = first_year_income * degradation_factor
        discounted_income = yearly_income / ((1 + params.discount_rate) ** (year - 1))
        total_discounted_income += discounted_income
    
    # 6. 贷款成本计算
    loan_amount = capex * params.loan_ratio
    annual_loan_payment = calculate_annuity_payment(loan_amount, params.loan_rate, params.loan_years)
    
    # 7. 总贷款成本 (只在贷款期内)
    total_loan_cost = 0
    for year in range(1, min(params.loan_years + 1, params.project_years + 1)):
        discounted_payment = annual_loan_payment / ((1 + params.discount_rate) ** (year - 1))
        total_loan_cost += discounted_payment
    
    # 8. 自有资金投入
    equity_investment = capex * (1 - params.loan_ratio)
    
    # 9. 弃电成本计算 (新增)
    # 弃电相当于浪费了可以创造收益的绿电，按上网电价计算损失
    first_year_curtailment_cost = annual_stats['total_curtailed'] * params.green_electricity_price
    total_discounted_curtailment_cost = 0
    for year in range(1, params.project_years + 1):
        # 光伏衰减影响弃电成本
        if year == 1:
            degradation_factor = 1 - params.pv_first_year_degradation
        else:
            degradation_factor = (1 - params.pv_first_year_degradation) * \
                               ((1 - params.pv_annual_degradation) ** (year - 2))
        
        yearly_curtailment_cost = first_year_curtailment_cost * degradation_factor
        discounted_curtailment_cost = yearly_curtailment_cost / ((1 + params.discount_rate) ** (year - 1))
        total_discounted_curtailment_cost += discounted_curtailment_cost
    
    # 10. LCOH计算 (单位：元/Nm³) - 净成本价，扣除售电收入
    # 总成本 = 自有资金 + 贷款成本 + 运维成本 + 弃电成本
    total_cost = equity_investment + total_loan_cost + total_discounted_opex

    # 净成本 = 总成本 - 售电收入
    net_cost = total_cost - total_discounted_income

    # 直接使用Nm³为单位计算LCOH
    if total_discounted_h2 > 0:
        lcoh = net_cost / total_discounted_h2  # 元/Nm³
    else:
        lcoh = float('inf')

    return lcoh

 