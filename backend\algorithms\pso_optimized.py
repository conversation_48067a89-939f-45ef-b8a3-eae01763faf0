#!/usr/bin/env python3
"""
优化的PSO算法实现
特点：
1. 0.1MW步长的离散化搜索
2. 缓存机制避免重复计算
3. 更高的计算效率
"""

import numpy as np
import time
import hashlib
from typing import List, Dict, Any, Tuple
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.electrolyzer_specs import ELECTROLYZER_SPECS, get_electrolyzer_spec
from algorithms.ems import simulate_8760_hours
from algorithms.economics import calculate_lcoh
from models.parameters import OptimizationResult
from utils.data_loader import load_pv_wind_data


class CachedEvaluator:
    """带缓存的评估器"""
    
    def __init__(self, electrolyzer_types: List[str], params):
        self.electrolyzer_types = electrolyzer_types
        self.params = params
        self.cache = {}
        self.hit_count = 0
        self.miss_count = 0
        
        # 加载光伏风机数据
        self.pv_curve, self.wind_curve = load_pv_wind_data()
        
    def _get_cache_key(self, particle: np.ndarray) -> str:
        """生成缓存键"""
        # 将粒子位置转换为0.1步长的离散值
        discrete_particle = self._discretize_particle(particle)
        # 使用元组作为缓存键
        key_tuple = tuple(discrete_particle)
        return str(key_tuple)
    
    def _discretize_particle(self, particle: np.ndarray) -> np.ndarray:
        """将粒子位置离散化为0.1MW步长"""
        discrete = particle.copy()
        
        # 容量维度（前3个）使用0.1MW步长
        for i in range(3):
            discrete[i] = round(discrete[i] * 10) / 10
        
        # 电解槽台数维度（后几个）保持整数
        for i in range(3, len(discrete)):
            discrete[i] = int(round(discrete[i]))
            
        return discrete
    
    def evaluate(self, particle: np.ndarray) -> float:
        """评估粒子适应度，使用缓存"""
        cache_key = self._get_cache_key(particle)
        
        # 检查缓存
        if cache_key in self.cache:
            self.hit_count += 1
            return self.cache[cache_key]
        
        # 缓存未命中，进行实际计算
        self.miss_count += 1
        discrete_particle = self._discretize_particle(particle)
        
        try:
            result = self._actual_evaluate(discrete_particle)
            # 存入缓存
            self.cache[cache_key] = result
            return result
        except Exception:
            # 约束违反，返回大数
            penalty = 1e10
            self.cache[cache_key] = penalty
            return penalty
    
    def _actual_evaluate(self, particle: np.ndarray) -> float:
        """实际的适应度评估（复用原有逻辑）"""
        # 解析粒子位置
        pv_capacity = max(0, particle[0])
        wind_capacity = max(0, particle[1])
        storage_capacity = max(0, particle[2])
        
        # 解析电解槽配置
        electrolyzer_config = []
        for i, elec_type in enumerate(self.electrolyzer_types):
            count = max(0, int(particle[3 + i]))
            if count > 0:
                electrolyzer_config.append({
                    'type': elec_type,
                    'count': count
                })
        
        # 基本约束检查
        if not self._check_basic_constraints(pv_capacity, wind_capacity, storage_capacity, electrolyzer_config):
            return 1e10
        
        # 构建粒子（用于兼容原有接口）- 必须包含所有电解槽类型的数量
        elec_counts = []
        for elec_type in self.electrolyzer_types:
            count = 0
            for config in electrolyzer_config:
                if config['type'] == elec_type:
                    count = config['count']
                    break
            elec_counts.append(count)
        
        particle_for_ems = np.array([pv_capacity, wind_capacity, storage_capacity] + elec_counts)
        
        # 运行EMS策略
        hourly_results, annual_stats = simulate_8760_hours(
            particle_for_ems, self.electrolyzer_types, self.pv_curve, self.wind_curve, self.params
        )
        
        # 检查电网约束
        if not self._check_grid_constraints(annual_stats):
            return 1e10
            
        # 检查弃电率硬约束
        total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
        if total_renewable > 0:
            curtailment_ratio = annual_stats['total_curtailed'] / total_renewable
            if curtailment_ratio > self.params.max_curtailment_ratio:
                return 1e10
        
        # 计算经济指标 - 使用原始的calculate_lcoh函数
        lcoh = calculate_lcoh(particle_for_ems, self.electrolyzer_types, annual_stats, self.params)
        
        # 应用产氢量指数惩罚
        final_lcoh = self._apply_h2_penalty(lcoh, annual_stats)
        
        return final_lcoh
    
    def _check_basic_constraints(self, pv_capacity: float, wind_capacity: float, 
                                storage_capacity: float, electrolyzer_config: List[Dict]) -> bool:
        """检查基本约束"""
        # 容量范围约束
        if not (self.params.pv_capacity_min <= pv_capacity <= self.params.pv_capacity_max):
            return False
        if not (self.params.wind_capacity_min <= wind_capacity <= self.params.wind_capacity_max):
            return False
        if not (self.params.storage_capacity_min <= storage_capacity <= self.params.storage_capacity_max):
            return False
        
        # 储能最小比例约束
        renewable_capacity = pv_capacity + wind_capacity
        if renewable_capacity > 0 and storage_capacity < renewable_capacity * self.params.storage_min_ratio:
            return False
        
        # 电解槽容量约束
        total_electrolyzer_capacity = sum(
            config['count'] * ELECTROLYZER_SPECS[config['type']]['rated_power'] 
            for config in electrolyzer_config
        )
        if not (self.params.electrolyzer_capacity_min <= total_electrolyzer_capacity <= self.params.electrolyzer_capacity_max):
            return False
        
        return True
    
    def _check_grid_constraints(self, annual_stats: Dict) -> bool:
        """检查电网约束"""
        total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
        if total_renewable == 0:
            return True
        
        grid_up_ratio = annual_stats['total_grid_up'] / total_renewable
        grid_down_ratio = annual_stats['total_grid_down'] / total_renewable
        
        return (grid_up_ratio <= self.params.max_grid_up_ratio and
                grid_down_ratio <= self.params.max_grid_down_ratio)
    

    
    def _apply_h2_penalty(self, base_lcoh: float, annual_stats: Dict) -> float:
        """应用产氢量指数惩罚"""
        actual_h2_tons = annual_stats['total_h2_production'] * 0.000089
        deviation_rate = abs(actual_h2_tons - self.params.target_h2_production) / self.params.target_h2_production
        
        if deviation_rate <= self.params.h2_production_tolerance:
            return base_lcoh
        
        excess_deviation = deviation_rate - self.params.h2_production_tolerance
        penalty_multiplier = self.params.h2_penalty_factor * (np.exp(excess_deviation) - 1)
        
        return base_lcoh * (1 + penalty_multiplier)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_evaluations = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_evaluations if total_evaluations > 0 else 0
        
        return {
            'total_evaluations': total_evaluations,
            'cache_hits': self.hit_count,
            'cache_misses': self.miss_count,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache)
        }


class OptimizedPSO:
    """优化的PSO算法类"""
    
    def __init__(self, electrolyzer_types: List[str], params):
        self.electrolyzer_types = electrolyzer_types
        self.params = params
        self.evaluator = CachedEvaluator(electrolyzer_types, params)
        self.particle_size = 3 + len(electrolyzer_types)
        self.bounds = self._setup_bounds()
        
        # PSO参数
        self.swarm_size = params.swarm_size
        self.max_iterations = params.max_iterations
        self.w_start = params.w_start
        self.w_end = params.w_end
        self.c1 = params.c1
        self.c2 = params.c2
        self.convergence_threshold = params.convergence_threshold
        
        # 初始化粒子群
        self.positions = None
        self.velocities = None
        self.personal_best_positions = None
        self.personal_best_fitness = None
        self.global_best_position = None
        self.global_best_fitness = float('inf')
        
        # 收敛跟踪
        self.fitness_history = []
        self.convergence_achieved = False
        
    def _setup_bounds(self) -> List[Tuple[float, float]]:
        """设置搜索边界"""
        bounds = [
            (self.params.pv_capacity_min, self.params.pv_capacity_max),
            (self.params.wind_capacity_min, self.params.wind_capacity_max),
            (self.params.storage_capacity_min, self.params.storage_capacity_max)
        ]
        
        # 添加电解槽台数边界
        for elec_type in self.electrolyzer_types:
            specs = ELECTROLYZER_SPECS[elec_type]
            max_count = int(self.params.electrolyzer_capacity_max / specs['rated_power'])
            bounds.append((0, max_count))
        
        return bounds
    
    def optimize(self) -> Dict[str, Any]:
        """执行优化"""
        start_time = time.time()
        
        # 初始化粒子群
        self._initialize_swarm()
        
        print(f"开始PSO优化，粒子群规模: {self.swarm_size}, 最大迭代次数: {self.max_iterations}")
        print(f"粒子维度: {self.particle_size} (选择了{len(self.electrolyzer_types)}种电解槽)")
        
        # 主优化循环
        for iteration in range(self.max_iterations):
            # 更新惯性权重
            w = self.w_start - (self.w_start - self.w_end) * iteration / self.max_iterations
            
            # 更新粒子
            self._update_particles(w)
            
            # 记录历史
            self.fitness_history.append(self.global_best_fitness)
            
            # 输出进度（每10代输出一次）
            if iteration % 10 == 0 or iteration == self.max_iterations - 1:
                self._print_progress(iteration)
            
            # 检查收敛
            if self._check_convergence():
                self.convergence_achieved = True
                print(f"算法在第{iteration}代收敛")
                break
        
        calculation_time = time.time() - start_time
        
        # 获取缓存统计
        cache_stats = self.evaluator.get_cache_stats()
        
        print(f"优化完成，耗时: {calculation_time:.2f}秒")
        print(f"缓存统计: 命中率{cache_stats['hit_rate']:.1%} ({cache_stats['cache_hits']}/{cache_stats['total_evaluations']})")
        
        # 构建最终结果
        return self._build_result(calculation_time, cache_stats)
    
    def _initialize_swarm(self):
        """初始化粒子群"""
        self.positions = np.random.uniform(
            low=[bound[0] for bound in self.bounds],
            high=[bound[1] for bound in self.bounds],
            size=(self.swarm_size, self.particle_size)
        )
        
        self.velocities = np.random.uniform(
            low=-1, high=1, size=(self.swarm_size, self.particle_size)
        )
        
        # 评估初始适应度
        self.personal_best_fitness = np.array([
            self.evaluator.evaluate(particle) for particle in self.positions
        ])
        
        self.personal_best_positions = self.positions.copy()
        
        # 找到全局最优
        best_idx = np.argmin(self.personal_best_fitness)
        self.global_best_fitness = self.personal_best_fitness[best_idx]
        self.global_best_position = self.positions[best_idx].copy()
    
    def _update_particles(self, w: float):
        """更新粒子位置和速度"""
        r1 = np.random.random((self.swarm_size, self.particle_size))
        r2 = np.random.random((self.swarm_size, self.particle_size))
        
        # 更新速度
        self.velocities = (w * self.velocities + 
                          self.c1 * r1 * (self.personal_best_positions - self.positions) +
                          self.c2 * r2 * (self.global_best_position - self.positions))
        
        # 更新位置
        self.positions += self.velocities
        
        # 边界处理
        for i in range(self.particle_size):
            self.positions[:, i] = np.clip(self.positions[:, i], 
                                         self.bounds[i][0], self.bounds[i][1])
        
        # 评估新位置
        for i in range(self.swarm_size):
            fitness = self.evaluator.evaluate(self.positions[i])
            
            # 更新个体最优
            if fitness < self.personal_best_fitness[i]:
                self.personal_best_fitness[i] = fitness
                self.personal_best_positions[i] = self.positions[i].copy()
                
                # 更新全局最优
                if fitness < self.global_best_fitness:
                    self.global_best_fitness = fitness
                    self.global_best_position = self.positions[i].copy()
    
    def _check_convergence(self) -> bool:
        """检查收敛条件"""
        if len(self.fitness_history) < 5:
            return False
        
        recent_improvements = [
            abs(self.fitness_history[-(i+1)] - self.fitness_history[-(i+2)])
            for i in range(4)
        ]
        
        return all(improvement < self.convergence_threshold for improvement in recent_improvements)
    
    def _print_progress(self, iteration: int):
        """打印优化进度"""
        best_particle = self.evaluator._discretize_particle(self.global_best_position)
        
        # 计算电解槽总容量
        total_elec_capacity = 0
        for i, elec_type in enumerate(self.electrolyzer_types):
            count = int(best_particle[3 + i])
            if count > 0:
                specs = ELECTROLYZER_SPECS[elec_type]
                total_elec_capacity += count * specs['rated_power']
        
        print(f"迭代{iteration:3d}: 最优LCOH={self.global_best_fitness:.6f}, "
              f"pv={best_particle[0]:.0f}, wind={best_particle[1]:.0f}, "
              f"bat={best_particle[2]:.0f}，alk={total_elec_capacity:.0f}MW")
    
    def _build_result(self, calculation_time: float, cache_stats: Dict) -> Dict[str, Any]:
        """构建最终结果"""
        # 使用最优粒子进行一次完整评估以获取详细结果
        best_particle = self.evaluator._discretize_particle(self.global_best_position)
        
        # 解析最优解
        pv_capacity = best_particle[0]
        wind_capacity = best_particle[1]
        storage_capacity = best_particle[2]
        
        electrolyzer_config = []
        for i, elec_type in enumerate(self.electrolyzer_types):
            count = int(best_particle[3 + i])
            if count > 0:
                specs = ELECTROLYZER_SPECS[elec_type]
                electrolyzer_config.append({
                    'type': elec_type,
                    'count': count,
                    'total_capacity': count * specs['rated_power']
                })
        
        # 运行EMS获取详细结果 - 修复粒子维度问题
        elec_counts = []
        for elec_type in self.electrolyzer_types:
            count = int(best_particle[3 + self.electrolyzer_types.index(elec_type)])
            elec_counts.append(count)
        
        particle_for_ems = np.array([pv_capacity, wind_capacity, storage_capacity] + elec_counts)
        hourly_results, annual_stats = simulate_8760_hours(
            particle_for_ems, self.electrolyzer_types, self.evaluator.pv_curve, 
            self.evaluator.wind_curve, self.params
        )
        
        # 计算经济指标
        total_capex = self._calculate_total_capex(pv_capacity, wind_capacity, storage_capacity, electrolyzer_config)
        basic_opex = self._calculate_basic_opex(pv_capacity, wind_capacity, storage_capacity, electrolyzer_config)
        
        # 计算比例
        total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
        grid_up_ratio = annual_stats['total_grid_up'] / total_renewable if total_renewable > 0 else 0
        grid_down_ratio = annual_stats['total_grid_down'] / total_renewable if total_renewable > 0 else 0
        curtailment_ratio = annual_stats['total_curtailed'] / total_renewable if total_renewable > 0 else 0
        
        # 计算其他经济指标
        water_cost = annual_stats['total_h2_production'] * 0.000089 * self.params.water_consumption * self.params.water_price
        grid_purchase_cost = annual_stats['total_grid_down'] * self.params.grid_purchase_price
        annual_income = annual_stats['total_grid_up'] * self.params.green_electricity_price
        total_opex = basic_opex + water_cost + grid_purchase_cost
        
        # 计算迭代次数
        optimization_iterations = len(self.fitness_history)
        
        result = OptimizationResult(
            optimal_lcoh=self.global_best_fitness,
            optimal_pv_capacity=pv_capacity,
            optimal_wind_capacity=wind_capacity,
            optimal_storage_capacity=storage_capacity,
            optimal_electrolyzer_config=electrolyzer_config,
            calculation_time=calculation_time,
            convergence_achieved=self.convergence_achieved,
            
            # 年度统计数据 - 字段名映射
            annual_h2_production=annual_stats['total_h2_production'],
            annual_pv_generation=annual_stats['total_pv_generation'],
            annual_wind_generation=annual_stats['total_wind_generation'],
            annual_grid_up=annual_stats['total_grid_up'],
            annual_grid_down=annual_stats['total_grid_down'],
            annual_curtailed=annual_stats['total_curtailed'],
            grid_up_ratio=grid_up_ratio,
            grid_down_ratio=grid_down_ratio,
            curtailment_ratio=curtailment_ratio,
            
            # 经济指标
            total_capex=total_capex,
            total_opex=total_opex,
            annual_income=annual_income,
            
            # 补充字段供测试脚本使用
            basic_opex=basic_opex,
            water_cost=water_cost,
            grid_purchase_cost=grid_purchase_cost,
            first_year_opex_total=total_opex,
            
            # 计算状态
            optimization_iterations=optimization_iterations,
            
            # 额外数据
            hourly_data=hourly_results
        )
        
        return result.model_dump()
    
    def _calculate_total_capex(self, pv_capacity: float, wind_capacity: float,
                              storage_capacity: float, electrolyzer_config: List[Dict]) -> float:
        """计算总投资成本"""
        pv_capex = pv_capacity * 1000 * self.params.pv_epc_cost
        wind_capex = wind_capacity * 1000 * self.params.wind_epc_cost
        storage_capex = storage_capacity * 1000 * self.params.storage_epc_cost
        
        electrolyzer_capex = 0
        for config in electrolyzer_config:
            specs = ELECTROLYZER_SPECS[config['type']]
            electrolyzer_capex += config['count'] * specs['epc_price']
        
        return pv_capex + wind_capex + storage_capex + electrolyzer_capex
    
    def _calculate_basic_opex(self, pv_capacity: float, wind_capacity: float,
                             storage_capacity: float, electrolyzer_config: List[Dict]) -> float:
        """计算基础运维成本"""
        electrolyzer_capex = sum(
            config['count'] * ELECTROLYZER_SPECS[config['type']]['epc_price']
            for config in electrolyzer_config
        )
        
        return (pv_capacity * 1000 * self.params.pv_om_cost +
                wind_capacity * 1000 * self.params.wind_om_cost +
                storage_capacity * 1000 * self.params.storage_om_cost +
                electrolyzer_capex * self.params.om_ratio) 