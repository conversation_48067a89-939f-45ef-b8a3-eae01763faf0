"""
EMS能量管理策略模块
"""

import numpy as np
from config.electrolyzer_specs import ELECTROLYZER_SPECS, get_actual_power_consumption
from models.parameters import OptimizationParameters

def calculate_storage_constraints(storage_capacity, charge_discharge_rate, current_soc, params: OptimizationParameters):
    """
    计算储能约束
    
    Args:
        storage_capacity: 储能容量 (MWh)
        charge_discharge_rate: 充放电倍率
        current_soc: 当前SOC
        params: 优化参数
    
    Returns:
        dict: 储能约束 {'max_charge': 负值, 'max_discharge': 正值}
    """
    
    # 最大充放电功率 (MW)
    max_charge_power = storage_capacity / charge_discharge_rate
    max_discharge_power = storage_capacity / charge_discharge_rate
    
    # 基于当前SOC的容量约束
    available_charge_capacity = storage_capacity * (params.soc_max - current_soc)  # 可充电容量
    available_discharge_capacity = storage_capacity * (current_soc - params.soc_min)  # 可放电容量
    
    # 考虑充放电效率的实际约束 (1小时内)
    max_actual_charge = min(max_charge_power, available_charge_capacity * params.charge_efficiency)
    max_actual_discharge = min(max_discharge_power, available_discharge_capacity / params.discharge_efficiency)
    
    return {
        'max_charge': max(0, max_actual_charge),     # 充电功率上限 (正值)
        'max_discharge': max(0, max_actual_discharge) # 放电功率上限 (正值)
    }

def allocate_insufficient_power(available_power, elec_config):
    """
    绿电不足时的电解槽功率分配策略 (小容量优先停机)
    
    Args:
        available_power: 可用功率 (MW)
        elec_config: 电解槽配置列表
    
    Returns:
        list: 分配结果列表
    """
    
    # 按额定功率从小到大排序（小容量先停机）
    sorted_elec = sorted(elec_config, key=lambda x: x['rated_power'])
    
    allocated_config = []
    remaining_power = available_power
    
    # 尝试让尽可能多的电解槽运行
    for elec in sorted_elec:
        min_power_per_unit = elec['rated_power'] * elec['min_load_rate']
        total_min_power = elec['count'] * min_power_per_unit
        
        if remaining_power >= total_min_power:
            # 这种类型的电解槽可以全部开机
            allocated_config.append({
                'type': elec['type'],
                'count': elec['count'],
                'running_count': elec['count'],
                'rated_power': elec['rated_power'],
                'min_load_rate': elec['min_load_rate'],
                'max_load_rate': elec['max_load_rate']
            })
            remaining_power -= total_min_power
        else:
            # 这种类型的电解槽部分开机
            running_count = int(remaining_power / min_power_per_unit)
            if running_count > 0:
                allocated_config.append({
                    'type': elec['type'],
                    'count': elec['count'],
                    'running_count': running_count,
                    'rated_power': elec['rated_power'],
                    'min_load_rate': elec['min_load_rate'],
                    'max_load_rate': elec['max_load_rate']
                })
                remaining_power -= running_count * min_power_per_unit
            break  # 后续更大容量的都无法开机
    
    # 剩余功率按比例分配给开机的电解槽
    allocation_details = []
    if allocated_config:
        total_running_capacity = sum([cfg['running_count'] * cfg['rated_power'] for cfg in allocated_config])
        
        for cfg in allocated_config:
            # 计算这种类型电解槽的额外功率分配
            type_capacity = cfg['running_count'] * cfg['rated_power']
            extra_power_ratio = type_capacity / total_running_capacity if total_running_capacity > 0 else 0
            extra_power = remaining_power * extra_power_ratio
            
            # 每台电解槽的总功率
            base_power = cfg['running_count'] * cfg['rated_power'] * cfg['min_load_rate']
            total_power = base_power + extra_power
            
            # 计算负载率
            avg_power_per_unit = total_power / cfg['running_count'] if cfg['running_count'] > 0 else 0
            load_rate = avg_power_per_unit / cfg['rated_power'] if cfg['rated_power'] > 0 else 0
            
            # 确保不超过最大负载率
            load_rate = min(load_rate, cfg['max_load_rate'])
            final_power = cfg['running_count'] * cfg['rated_power'] * load_rate
            
            allocation_details.append({
                'type': cfg['type'],
                'total_power': final_power,
                'running_count': cfg['running_count'],
                'stopped_count': cfg['count'] - cfg['running_count'],
                'load_rate': load_rate
            })
    
    return allocation_details

def ems_power_allocation(hour, pv_power, wind_power, storage_soc, elec_config, storage_capacity, params: OptimizationParameters, annual_stats=None, max_annual_grid_up=None, max_annual_grid_down=None):
    """
    EMS功率分配策略 - 使用固定的年度电网限制实现贪心上网
    
    Args:
        hour: 当前小时
        pv_power: 光伏功率(MW)  
        wind_power: 风机功率(MW)
        storage_soc: 当前储能SOC
        elec_config: 电解槽配置
        storage_capacity: 储能容量(MWh)
        params: 优化参数
        annual_stats: 年度累计统计
        max_annual_grid_up: 固定的年度最大上网量(MWh)
        max_annual_grid_down: 固定的年度最大下网量(MWh)
    
    Returns:
        dict: 功率分配结果
    """
    
    renewable_power = pv_power + wind_power
    
    # 计算电解槽总容量
    total_min_load = sum([
        elec['count'] * elec['rated_power'] * elec['min_load_rate'] 
        for elec in elec_config
    ])
    total_max_load = sum([
        elec['count'] * elec['rated_power'] * elec['max_load_rate'] 
        for elec in elec_config  
    ])
    
    # 计算储能约束
    storage_constraints = calculate_storage_constraints(
        storage_capacity, params.charge_discharge_rate, storage_soc, params
    )
    
    allocation = {
        'electrolyzer_power': 0,
        'storage_power': 0,      # 正值=放电，负值=充电
        'grid_power': 0,         # 正值=下网，负值=上网
        'curtailed_power': 0,
        'electrolyzer_details': []
    }
    
    # 计算当前年度累计比例（如果提供了annual_stats）
    current_total_renewable = 0
    current_grid_up_ratio = 0
    current_grid_down_ratio = 0
    
    if annual_stats:
        current_total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
        if current_total_renewable > 0:
            current_grid_up_ratio = annual_stats['total_grid_up'] / current_total_renewable
            current_grid_down_ratio = annual_stats['total_grid_down'] / current_total_renewable
    
    if renewable_power >= total_max_load:
        # 情况2.1：绿电充足 - 电解槽满功率，储能充电，上网（考虑比例限制），弃电
        allocation['electrolyzer_power'] = total_max_load
        remaining_power = renewable_power - total_max_load
        
        # 均分给各电解槽（满功率运行）
        for elec in elec_config:
            elec_power = elec['count'] * elec['rated_power'] * elec['max_load_rate']
            allocation['electrolyzer_details'].append({
                'type': elec['type'],
                'total_power': elec_power,
                'running_count': elec['count'],
                'stopped_count': 0,
                'load_rate': elec['max_load_rate']
            })
        
        # 优先储能充电
        storage_charge = min(remaining_power, storage_constraints['max_charge'])
        allocation['storage_power'] = -storage_charge  # 负值表示充电
        remaining_power -= storage_charge
        
        # 贪心上网策略：优先上网到年度限制，剩余才弃电
        if remaining_power > 0:
            max_grid_up_power = 0  # 默认不上网
            
            if annual_stats and max_annual_grid_up is not None:
                # 计算剩余上网配额
                current_grid_up = annual_stats['total_grid_up']
                available_grid_up_quota = max_annual_grid_up - current_grid_up
                
                if available_grid_up_quota > 0.001:  # 避免浮点数精度问题
                    # 还有上网配额，贪心上网（能上多少上多少）
                    max_grid_up_power = min(remaining_power, available_grid_up_quota)
                    
                    # 调试信息（仅在接近限制时显示）
                    # if available_grid_up_quota < 10:  # 剩余配额小于10MWh时显示
                    #     print(f"小时{hour}: 剩余上网配额 {available_grid_up_quota:.2f}MWh, 本次上网 {max_grid_up_power:.2f}MW")
                else:
                    # 已达年度上网限制，不能再上网
                    max_grid_up_power = 0
                    # if remaining_power > 0.1:  # 有较大弃电时显示
                    #     print(f"小时{hour}: 上网限制已满，弃电 {remaining_power:.2f}MW")
            else:
                # 无年度限制时，全部上网
                max_grid_up_power = remaining_power
            
            # 分配上网和弃电
            allocation['grid_power'] = -max_grid_up_power  # 负值表示上网
            allocation['curtailed_power'] = remaining_power - max_grid_up_power
        
    elif renewable_power >= total_min_load:
        # 情况2.2：绿电不足但可维持最低负荷 - 电解槽消纳绿电，储能和电网不动
        allocation['electrolyzer_power'] = renewable_power
        
        # 按比例分配给各电解槽
        if total_max_load > total_min_load:
            available_power = renewable_power - total_min_load
            ratio = available_power / (total_max_load - total_min_load)
        else:
            ratio = 0
        
        for elec in elec_config:
            elec_min_power = elec['count'] * elec['rated_power'] * elec['min_load_rate']
            elec_max_power = elec['count'] * elec['rated_power'] * elec['max_load_rate']
            
            elec_power = elec_min_power + ratio * (elec_max_power - elec_min_power)
            load_rate = elec_power / (elec['count'] * elec['rated_power']) if elec['count'] > 0 else 0
            
            allocation['electrolyzer_details'].append({
                'type': elec['type'],
                'total_power': elec_power,
                'running_count': elec['count'],
                'stopped_count': 0,
                'load_rate': load_rate
            })
    
    else:
        # 情况2.3：绿电不足 - 储能放电，电网下网（考虑比例限制），部分电解槽停机
        required_power = total_min_load - renewable_power
        
        # 优先储能放电
        storage_discharge = min(required_power, storage_constraints['max_discharge'])
        allocation['storage_power'] = storage_discharge  # 正值表示放电
        remaining_required = required_power - storage_discharge
        
        # 电网下网（考虑年度比例限制）
        grid_down = 0
        if remaining_required > 0:
            # 检查下网是否会超过年度限制
            can_grid_down = True
            if annual_stats and current_total_renewable > 0:
                # 预测本小时后的累计比例
                future_total_renewable = current_total_renewable + renewable_power
                future_grid_down = annual_stats['total_grid_down'] + remaining_required
                future_grid_down_ratio = future_grid_down / future_total_renewable
                
                if future_grid_down_ratio > params.max_grid_down_ratio:
                    can_grid_down = False
            
            if can_grid_down:
                grid_down = remaining_required
            else:
                # 超过年度下网限制，只能取部分电或不取电
                if annual_stats and current_total_renewable > 0:
                    future_total_renewable = current_total_renewable + renewable_power
                    max_allowed_grid_down = future_total_renewable * params.max_grid_down_ratio - annual_stats['total_grid_down']
                    grid_down = max(0, min(remaining_required, max_allowed_grid_down))
        
        allocation['grid_power'] = grid_down  # 正值表示下网
        
        # 计算实际可用功率
        available_power = renewable_power + storage_discharge + grid_down
        
        if available_power >= total_min_load:
            # 可以维持所有电解槽最低负荷
            allocation['electrolyzer_power'] = total_min_load
            
            for elec in elec_config:
                elec_power = elec['count'] * elec['rated_power'] * elec['min_load_rate']
                allocation['electrolyzer_details'].append({
                    'type': elec['type'],
                    'total_power': elec_power,
                    'running_count': elec['count'],
                    'stopped_count': 0,
                    'load_rate': elec['min_load_rate']
                })
        else:
            # 需要部分电解槽停机
            allocation['electrolyzer_details'] = allocate_insufficient_power(available_power, elec_config)
            allocation['electrolyzer_power'] = sum([detail['total_power'] for detail in allocation['electrolyzer_details']])
    
    return allocation

def simulate_8760_hours(particle, selected_types, pv_curve, wind_curve, params: OptimizationParameters):
    """
    8760小时仿真 - 修改为支持年度电网比例约束检查
    
    Args:
        particle: 优化粒子
        selected_types: 选择的电解槽类型
        pv_curve: 光伏出力曲线
        wind_curve: 风机出力曲线
        params: 优化参数
    
    Returns:
        tuple: (hourly_results, annual_stats)
    """
    
    pv_capacity, wind_capacity, storage_capacity = particle[:3]
    elec_counts = particle[3:]
    
    # 构建电解槽配置
    elec_config = []
    for i, elec_type in enumerate(selected_types):
        elec_spec = ELECTROLYZER_SPECS[elec_type]
        elec_config.append({
            'type': elec_type,
            'count': int(elec_counts[i]),
            'rated_power': elec_spec['rated_power'],
            'min_load_rate': elec_spec['min_load_rate'],
            'max_load_rate': elec_spec['max_load_rate'],
        })
    
    # 预计算年度总发电量（用于固定上网限制）
    annual_total_pv = sum(pv_capacity * pv_curve[hour] for hour in range(8760))
    annual_total_wind = sum(wind_capacity * wind_curve[hour] for hour in range(8760))
    annual_total_renewable = annual_total_pv + annual_total_wind
    
    # 计算固定的年度上网限制
    max_annual_grid_up = annual_total_renewable * params.max_grid_up_ratio
    max_annual_grid_down = annual_total_renewable * params.max_grid_down_ratio
    
    # print(f"年度总发电量: {annual_total_renewable:.1f} MWh")
    # print(f"年度上网限制: {max_annual_grid_up:.1f} MWh ({params.max_grid_up_ratio*100:.1f}%)")
    # print(f"年度下网限制: {max_annual_grid_down:.1f} MWh ({params.max_grid_down_ratio*100:.1f}%)")
    
    # 初始化
    soc = params.initial_soc
    hourly_results = []
    
    # 累计统计
    annual_stats = {
        'total_pv_generation': 0,
        'total_wind_generation': 0,
        'total_grid_down': 0,
        'total_grid_up': 0,
        'total_h2_production': 0,
        'total_curtailed': 0,
        # 新增小时数统计
        'pv_generation_hours': 0,
        'wind_generation_hours': 0,
        'renewable_generation_hours': 0,
        'electrolyzer_running_hours': 0,
        'storage_charge_hours': 0,
        'storage_discharge_hours': 0,
        'grid_up_hours': 0,
        'grid_down_hours': 0
    }
    
    for hour in range(8760):
        # 计算当前小时新能源出力
        pv_power = pv_capacity * pv_curve[hour]  # 第一年无衰减
        wind_power = wind_capacity * wind_curve[hour]
        
        # EMS功率分配（传入固定的年度限制）
        allocation = ems_power_allocation(
            hour, pv_power, wind_power, soc, elec_config, storage_capacity, params, 
            annual_stats, max_annual_grid_up, max_annual_grid_down
        )
        
        # 更新储能SOC
        storage_energy_change = allocation['storage_power'] * 1  # 1小时
        if allocation['storage_power'] > 0:  # 放电
            soc_change = -storage_energy_change / (storage_capacity * params.discharge_efficiency)
        elif allocation['storage_power'] < 0:  # 充电
            soc_change = -storage_energy_change * params.charge_efficiency / storage_capacity
        else:
            soc_change = 0
        
        new_soc = soc + soc_change
        new_soc = max(params.soc_min, min(params.soc_max, new_soc))
        
        # 计算产氢量 - 使用能耗曲线
        h2_production = 0
        for elec_detail in allocation['electrolyzer_details']:
            # 使用动态能耗曲线计算实际电耗
            actual_consumption = get_actual_power_consumption(
                elec_detail['type'], 
                elec_detail['load_rate']
            )
            
            # 产氢量 = 功率 * 时间 / 实际电耗 (Nm³)
            h2_production += elec_detail['total_power'] * 1000 * 1 / actual_consumption  # MW转kW
        
        # 记录结果
        hour_result = {
            'hour': hour,
            'pv_power': pv_power,
            'wind_power': wind_power,
            'renewable_power': pv_power + wind_power,
            'storage_power': allocation['storage_power'],
            'storage_soc': new_soc,
            'grid_power': allocation['grid_power'],
            'electrolyzer_total_power': allocation['electrolyzer_power'],
            'electrolyzer_details': allocation['electrolyzer_details'],
            'h2_production': h2_production,
            'curtailed_power': allocation['curtailed_power']
        }
        
        hourly_results.append(hour_result)
        
        # 更新累计统计
        annual_stats['total_pv_generation'] += pv_power
        annual_stats['total_wind_generation'] += wind_power
        annual_stats['total_grid_down'] += max(0, allocation['grid_power'])
        annual_stats['total_grid_up'] += max(0, -allocation['grid_power'])
        annual_stats['total_h2_production'] += h2_production
        annual_stats['total_curtailed'] += allocation['curtailed_power']
        
        # 小时数统计逻辑已改为模拟结束后计算等效小时数
        # 这里不再做计数统计
        
        # 更新SOC
        soc = new_soc
    
    return hourly_results, annual_stats 