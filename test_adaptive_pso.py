#!/usr/bin/env python3
"""
自适应PSO算法测试脚本
对比原始PSO和自适应PSO的性能
"""

import sys
import os
import json
import time
from datetime import datetime

sys.path.append('./backend')

from models.parameters import OptimizationParameters
from algorithms.pso import DynamicPSO
from algorithms.adaptive_pso import AdaptivePSO

def save_result_to_output(result, params, algorithm_name):
    """将结果保存到output文件夹"""
    
    # 确保output目录存在
    output_dir = "./output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成结果ID
    existing_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]
    result_id = f"{len(existing_files) + 1}"
    
    # 添加计算信息
    calculation_info = {
        "result_id": result_id,
        "algorithm": algorithm_name,
        "calculation_time": datetime.now().isoformat(),
        "parameters": params.model_dump(),
        "selected_electrolyzer_types": params.selected_electrolyzer_types
    }
    
    # 合并结果
    final_result = {**result, **calculation_info}
    
    # 保存结果到文件
    result_file = os.path.join(output_dir, f"{result_id}.json")
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✓ 结果已保存到: {result_file}")
    return result_id

def create_test_parameters():
    """创建测试参数"""
    return OptimizationParameters(
        # 项目经济参数
        project_years=25,
        target_h2_production=12000,
        loan_years=15,
        loan_ratio=0.7,
        loan_rate=0.031,
        discount_rate=0.06,
        
        # 设备参数
        pv_capacity_min=0,
        pv_capacity_max=200,
        pv_epc_cost=3.5,
        pv_om_cost=0.08,
        
        wind_capacity_min=0,
        wind_capacity_max=200,
        wind_epc_cost=4.2,
        wind_om_cost=0.08,
        
        storage_capacity_min=0,
        storage_capacity_max=100,
        storage_min_ratio=0.1,
        storage_epc_cost=1.2,
        storage_om_cost=0.06,
        charge_efficiency=0.95,
        discharge_efficiency=0.95,
        charge_discharge_rate=0.5,
        initial_soc=0.5,
        soc_min=0.1,
        soc_max=0.9,
        
        # 电解槽参数
        selected_electrolyzer_types=["G_2000", "G_3000", "P_200"],
        electrolyzer_capacity_min=0,
        electrolyzer_capacity_max=200,
        water_price=4.38,
        water_consumption=1.4,
        om_ratio=0.04,
        
        # 约束参数
        max_grid_down_ratio=0.03,
        max_grid_up_ratio=0.03,
        max_curtailment_ratio=0.05,
        grid_purchase_price=0.45,
        green_electricity_price=0.2829,
        
        # PSO算法参数
        swarm_size=50,
        max_iterations=100,
        w_start=0.9,
        w_end=0.4,
        c1=2.0,
        c2=2.0,
        convergence_threshold=1e-3,
        constraint_penalty=1e6,
        h2_production_tolerance=0.02,
        h2_penalty_factor=50.0,
    )

def test_original_pso(params):
    """测试原始PSO"""
    print("=== 测试原始PSO ===")
    
    try:
        # 创建原始PSO优化器
        pso = DynamicPSO(params.selected_electrolyzer_types, params)
        
        print(f"✓ 原始PSO创建成功")
        
        # 执行优化
        start_time = time.time()
        result = pso.optimize()
        end_time = time.time()
        
        # 添加算法标识
        result['algorithm'] = 'OriginalPSO'
        result['calculation_time'] = end_time - start_time
        
        # 输出结果
        print(f"\n=== 原始PSO结果 ===")
        print(f"✓ 最优LCOH: {result['optimal_lcoh']:.6f} 元/Nm³")
        print(f"✓ 光伏容量: {result['optimal_pv_capacity']:.1f} MW")
        print(f"✓ 风机容量: {result['optimal_wind_capacity']:.1f} MW")
        print(f"✓ 储能容量: {result['optimal_storage_capacity']:.1f} MWh")
        print(f"✓ 年产氢量: {result['annual_h2_production']*0.000089:.0f} 吨")
        print(f"✓ 计算时间: {result['calculation_time']:.1f} 秒")
        print(f"✓ 收敛状态: {'是' if result['convergence_achieved'] else '否'}")
        
        # 保存结果
        result_id = save_result_to_output(result, params, 'OriginalPSO')
        
        return result
        
    except Exception as e:
        print(f"❌ 原始PSO测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_adaptive_pso(params):
    """测试自适应PSO"""
    print("\n=== 测试自适应PSO ===")
    
    try:
        # 创建自适应PSO优化器
        adaptive_pso = AdaptivePSO(params.selected_electrolyzer_types, params)
        
        print(f"✓ 自适应PSO创建成功")
        
        # 执行优化
        result = adaptive_pso.optimize()
        
        if 'error' in result:
            print(f"❌ 自适应PSO优化失败: {result['error']}")
            return None
        
        # 输出结果
        print(f"\n=== 自适应PSO结果 ===")
        print(f"✓ 最优LCOH: {result['optimal_lcoh']:.6f} 元/Nm³")
        print(f"✓ 光伏容量: {result['optimal_pv_capacity']:.1f} MW")
        print(f"✓ 风机容量: {result['optimal_wind_capacity']:.1f} MW")
        print(f"✓ 储能容量: {result['optimal_storage_capacity']:.1f} MWh")
        print(f"✓ 年产氢量: {result['annual_h2_production']*0.000089:.0f} 吨")
        print(f"✓ 计算时间: {result['calculation_time']:.1f} 秒")
        print(f"✓ 收敛状态: {'是' if result['convergence_achieved'] else '否'}")
        
        # 自适应特性
        adaptive_info = result.get('adaptive_features', {})
        print(f"\n✓ 自适应特性:")
        print(f"  - 最终多样性: {adaptive_info.get('final_diversity', 0):.3f}")
        print(f"  - 适应度改善率: {adaptive_info.get('fitness_improvement_rate', 0):.3f}")
        print(f"  - 多样性维护次数: {adaptive_info.get('diversity_maintenance_triggered', 0)}")
        
        # 保存结果
        result_id = save_result_to_output(result, params, 'AdaptivePSO')
        
        # 获取自适应统计
        stats = adaptive_pso.get_adaptive_statistics()
        if 'error' not in stats:
            print(f"\n✓ 自适应统计:")
            diversity_stats = stats['diversity_stats']
            print(f"  - 多样性范围: {diversity_stats['min']:.3f} - {diversity_stats['max']:.3f}")
            print(f"  - 平均多样性: {diversity_stats['avg']:.3f}")
            
            maintenance = stats['maintenance_events']
            print(f"  - 低多样性事件: {maintenance['low_diversity_count']}")
            print(f"  - 高多样性事件: {maintenance['high_diversity_count']}")
        
        return result
        
    except Exception as e:
        print(f"❌ 自适应PSO测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_results(original_result, adaptive_result):
    """对比两种算法的结果"""
    if not original_result or not adaptive_result:
        print("⚠️ 无法进行对比，某个算法测试失败")
        return
    
    print(f"\n{'='*60}")
    print(f"算法性能对比")
    print(f"{'='*60}")
    
    # 性能指标对比
    print(f"{'指标':<20} {'原始PSO':<15} {'自适应PSO':<15} {'改进':<15}")
    print(f"{'-'*65}")
    
    # LCOH对比
    orig_lcoh = original_result['optimal_lcoh']
    adapt_lcoh = adaptive_result['optimal_lcoh']
    lcoh_improvement = (orig_lcoh - adapt_lcoh) / orig_lcoh * 100
    print(f"{'LCOH(元/Nm³)':<20} {orig_lcoh:<15.6f} {adapt_lcoh:<15.6f} {lcoh_improvement:+.2f}%")
    
    # 计算时间对比
    orig_time = original_result['calculation_time']
    adapt_time = adaptive_result['calculation_time']
    time_change = (adapt_time - orig_time) / orig_time * 100
    print(f"{'计算时间(秒)':<20} {orig_time:<15.1f} {adapt_time:<15.1f} {time_change:+.2f}%")
    
    # 收敛性对比
    orig_conv = original_result['convergence_achieved']
    adapt_conv = adaptive_result['convergence_achieved']
    print(f"{'收敛状态':<20} {'是' if orig_conv else '否':<15} {'是' if adapt_conv else '否':<15} {'--':<15}")
    
    # 迭代次数对比
    orig_iter = original_result.get('optimization_iterations', 0)
    adapt_iter = adaptive_result.get('optimization_iterations', 0)
    iter_change = (adapt_iter - orig_iter) / orig_iter * 100 if orig_iter > 0 else 0
    print(f"{'迭代次数':<20} {orig_iter:<15} {adapt_iter:<15} {iter_change:+.2f}%")
    
    # 产氢量对比
    orig_h2 = original_result['annual_h2_production'] * 0.000089
    adapt_h2 = adaptive_result['annual_h2_production'] * 0.000089
    h2_change = (adapt_h2 - orig_h2) / orig_h2 * 100
    print(f"{'产氢量(吨)':<20} {orig_h2:<15.0f} {adapt_h2:<15.0f} {h2_change:+.2f}%")
    
    # 总结
    print(f"\n✓ 性能总结:")
    if lcoh_improvement > 0:
        print(f"  🎉 自适应PSO的LCOH降低了 {lcoh_improvement:.2f}%")
    elif lcoh_improvement < -1:
        print(f"  ⚠️ 自适应PSO的LCOH增加了 {abs(lcoh_improvement):.2f}%")
    else:
        print(f"  ➡️ 两种算法的LCOH基本相当")
    
    if adapt_conv and not orig_conv:
        print(f"  ✅ 自适应PSO实现了收敛，原始PSO未收敛")
    elif not adapt_conv and orig_conv:
        print(f"  ⚠️ 原始PSO收敛，但自适应PSO未收敛")
    
    if abs(time_change) < 20:
        print(f"  ⏱️ 计算时间相当 ({time_change:+.1f}%)")
    elif time_change > 20:
        print(f"  ⏱️ 自适应PSO计算时间增加了 {time_change:.1f}%")
    else:
        print(f"  ⏱️ 自适应PSO计算时间减少了 {abs(time_change):.1f}%")

def main():
    """主测试函数"""
    print("=== 自适应PSO vs 原始PSO 对比测试 ===\n")
    
    # 创建测试参数
    params = create_test_parameters()
    print(f"✓ 测试参数创建完成")
    print(f"  - 粒子群规模: {params.swarm_size}")
    print(f"  - 最大迭代次数: {params.max_iterations}")
    print(f"  - 电解槽类型: {params.selected_electrolyzer_types}")
    
    # 测试原始PSO
    original_result = test_original_pso(params)
    
    # 测试自适应PSO
    adaptive_result = test_adaptive_pso(params)
    
    # 对比结果
    compare_results(original_result, adaptive_result)
    
    print(f"\n🎉 自适应PSO测试完成！")
    if original_result and adaptive_result:
        print(f"✅ 两种算法都成功运行，已生成对比分析")
    else:
        print(f"⚠️ 部分算法测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
