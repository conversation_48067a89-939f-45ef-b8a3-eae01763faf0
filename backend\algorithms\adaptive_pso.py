#!/usr/bin/env python3
"""
自适应PSO算法实现 - 阶段1：基础自适应
特点：
1. 自适应惯性权重 (多因子)
2. 多样性监控与维护
3. 智能约束处理
4. 自适应学习因子
"""

import numpy as np
import random
import time
import math
from typing import List, Dict, Any, Tuple
from config.electrolyzer_specs import ELECTROLYZER_SPECS
from models.parameters import OptimizationParameters
from algorithms.ems import simulate_8760_hours
from algorithms.economics import calculate_lcoh
from utils.data_loader import load_pv_wind_data

class AdaptivePSO:
    """自适应PSO算法类"""
    
    def __init__(self, selected_types: List[str], params: OptimizationParameters):
        self.selected_types = selected_types
        self.params = params
        
        # PSO基本参数
        self.swarm_size = params.swarm_size
        self.max_iterations = params.max_iterations
        self.particle_size = 3 + len(selected_types)
        
        # 自适应参数
        self.w_base = 0.7           # 基础惯性权重
        self.c1_base = 2.0          # 基础个体学习因子
        self.c2_base = 2.0          # 基础社会学习因子
        
        # 多样性维护参数
        self.diversity_threshold_low = 0.1    # 多样性过低阈值
        self.diversity_threshold_high = 0.8   # 多样性过高阈值
        self.mutation_rate = 0.05             # 变异率
        
        # 约束处理参数
        self.constraint_tolerance = 0.01      # 约束容忍度
        self.penalty_escalation = 1.5         # 惩罚递增因子
        
        # 历史记录
        self.diversity_history = []
        self.fitness_improvement_history = []
        self.personal_improvement_history = []  # 每个粒子的改善历史
        self.success_rates = {'personal': [], 'global': []}
        self.best_scores_history = []
        
        # 数据加载
        self.pv_curve, self.wind_curve = load_pv_wind_data()
        
        # 设置边界
        self.bounds = self._setup_bounds()
        
        # 初始化粒子群
        self.swarm = []
        self.velocities = []
        self.personal_best_positions = []
        self.personal_best_scores = []
        self.global_best_position = None
        self.global_best_score = float('inf')
        
        # 收敛控制
        self.convergence_count = 0
        self.convergence_threshold = params.convergence_threshold
        
        print(f"✓ 自适应PSO初始化完成")
        print(f"  - 粒子群规模: {self.swarm_size}")
        print(f"  - 粒子维度: {self.particle_size}")
        print(f"  - 多样性阈值: {self.diversity_threshold_low} - {self.diversity_threshold_high}")
    
    def _setup_bounds(self) -> List[Tuple[float, float]]:
        """设置优化边界"""
        bounds = [
            (self.params.pv_capacity_min, self.params.pv_capacity_max),
            (self.params.wind_capacity_min, self.params.wind_capacity_max),
            (self.params.storage_capacity_min, self.params.storage_capacity_max)
        ]
        
        # 电解槽数量边界
        for elec_type in self.selected_types:
            elec_spec = ELECTROLYZER_SPECS[elec_type]
            max_count = int(self.params.electrolyzer_capacity_max / elec_spec['rated_power']) + 1
            bounds.append((0, max_count))
        
        return bounds
    
    def calculate_population_diversity(self) -> float:
        """计算种群多样性"""
        if len(self.swarm) < 2:
            return 1.0
        
        n_particles = len(self.swarm)
        total_distance = 0
        count = 0
        
        # 计算所有粒子对之间的距离
        for i in range(n_particles):
            for j in range(i + 1, n_particles):
                # 归一化距离计算
                distance = 0
                for k in range(self.particle_size):
                    low, high = self.bounds[k]
                    if high > low:
                        norm_diff = (self.swarm[i][k] - self.swarm[j][k]) / (high - low)
                        distance += norm_diff ** 2
                
                distance = math.sqrt(distance)
                total_distance += distance
                count += 1
        
        # 归一化多样性指标
        avg_distance = total_distance / count if count > 0 else 0
        max_possible_distance = math.sqrt(self.particle_size)  # 理论最大距离
        diversity = avg_distance / max_possible_distance
        
        return min(1.0, diversity)
    
    def calculate_fitness_improvement(self) -> float:
        """计算适应度改善率"""
        if len(self.best_scores_history) < 10:
            return 0.5  # 默认中等改善率
        
        # 计算最近10代的改善率
        recent_best = self.best_scores_history[-10]
        current_best = self.global_best_score
        
        if recent_best > 0:
            improvement = (recent_best - current_best) / recent_best
            return max(0, min(1, improvement))
        
        return 0.5
    
    def calculate_personal_success_rate(self, particle_idx: int) -> float:
        """计算个体成功率"""
        if particle_idx >= len(self.personal_improvement_history):
            return 0.5
        
        recent_improvements = self.personal_improvement_history[particle_idx][-10:]
        if not recent_improvements:
            return 0.5
        
        success_rate = sum(recent_improvements) / len(recent_improvements)
        return success_rate
    
    def calculate_adaptive_inertia(self, iteration: int, diversity: float, 
                                 fitness_improvement: float) -> float:
        """计算自适应惯性权重"""
        # 基础时间衰减
        w_time = 0.9 - 0.5 * (iteration / self.max_iterations)
        
        # 多样性因子 (多样性低时增加探索)
        w_diversity = 0.2 * (1 - diversity) if diversity < 0.3 else 0
        
        # 适应度改善因子 (改善慢时增加探索)
        w_fitness = 0.1 * (1 - fitness_improvement) if fitness_improvement < 0.01 else 0
        
        # 综合权重 (限制在合理范围)
        w_adaptive = w_time + w_diversity + w_fitness
        w_final = max(0.1, min(0.9, w_adaptive))
        
        return w_final
    
    def calculate_adaptive_learning_factors(self, iteration: int, 
                                          personal_success: float) -> Tuple[float, float]:
        """计算自适应学习因子"""
        # 基础值
        c1_base, c2_base = self.c1_base, self.c2_base
        
        # 个体成功率影响个体学习
        c1 = c1_base * (0.5 + 0.5 * personal_success)
        
        # 全局成功率影响社会学习
        global_success = self.calculate_fitness_improvement()
        c2 = c2_base * (0.5 + 0.5 * global_success)
        
        # 后期增强局部搜索
        if iteration > self.max_iterations * 0.7:
            c1 *= 0.8
            c2 *= 1.2
        
        return c1, c2
    
    def calculate_constraint_violation(self, particle: List[float]) -> Tuple[float, Dict[str, float]]:
        """计算约束违反程度"""
        violations = {}
        
        pv_cap, wind_cap, storage_cap = particle[:3]
        
        # 容量约束违反
        if pv_cap < self.params.pv_capacity_min:
            violations['pv_min'] = self.params.pv_capacity_min - pv_cap
        if pv_cap > self.params.pv_capacity_max:
            violations['pv_max'] = pv_cap - self.params.pv_capacity_max
        
        if wind_cap < self.params.wind_capacity_min:
            violations['wind_min'] = self.params.wind_capacity_min - wind_cap
        if wind_cap > self.params.wind_capacity_max:
            violations['wind_max'] = wind_cap - self.params.wind_capacity_max
        
        if storage_cap < self.params.storage_capacity_min:
            violations['storage_min'] = self.params.storage_capacity_min - storage_cap
        if storage_cap > self.params.storage_capacity_max:
            violations['storage_max'] = storage_cap - self.params.storage_capacity_max
        
        # 储能比例约束
        renewable_cap = pv_cap + wind_cap
        if renewable_cap > 0:
            required_storage = renewable_cap * self.params.storage_min_ratio
            if storage_cap < required_storage:
                violations['storage_ratio'] = required_storage - storage_cap
        
        # 电解槽容量约束
        elec_counts = particle[3:]
        total_elec_cap = 0
        for i, elec_type in enumerate(self.selected_types):
            elec_spec = ELECTROLYZER_SPECS[elec_type]
            total_elec_cap += elec_counts[i] * elec_spec['rated_power']
        
        if total_elec_cap > self.params.electrolyzer_capacity_max:
            violations['elec_max'] = total_elec_cap - self.params.electrolyzer_capacity_max
        
        # 计算总违反度
        total_violation = sum(violations.values())
        return total_violation, violations
    
    def calculate_adaptive_penalty(self, violation_degree: float, 
                                 violations_detail: Dict[str, float], 
                                 iteration: int) -> float:
        """计算自适应惩罚"""
        if violation_degree == 0:
            return 0
        
        # 基础惩罚强度 (后期增强)
        base_penalty = 1000 * (1 + iteration / self.max_iterations)
        
        # 违反类型权重
        violation_weights = {
            'pv_min': 1.0, 'pv_max': 1.0,
            'wind_min': 1.0, 'wind_max': 1.0,
            'storage_min': 1.0, 'storage_max': 1.0,
            'storage_ratio': 2.0,  # 储能比例约束更重要
            'elec_max': 1.5,       # 电解槽容量约束
        }
        
        # 计算加权惩罚
        weighted_penalty = 0
        for violation_type, amount in violations_detail.items():
            weight = violation_weights.get(violation_type, 1.0)
            weighted_penalty += weight * amount * base_penalty
        
        return weighted_penalty

    def maintain_diversity(self, diversity: float, iteration: int):
        """多样性维护"""
        if diversity < self.diversity_threshold_low:
            print(f"  多样性过低({diversity:.3f})，启动多样性维护")

            # 策略1: 重新初始化最差的20%粒子
            n_reinit = max(1, int(self.swarm_size * 0.2))
            worst_indices = sorted(range(self.swarm_size),
                                 key=lambda i: self.personal_best_scores[i],
                                 reverse=True)[:n_reinit]

            for idx in worst_indices:
                # 重新初始化粒子
                particle = []
                velocity = []

                for j, (low, high) in enumerate(self.bounds):
                    if j < 3:  # 连续变量
                        pos = random.uniform(low, high)
                        vel = random.uniform(-abs(high-low)*0.1, abs(high-low)*0.1)
                    else:  # 整数变量
                        pos = random.randint(low, high)
                        vel = random.uniform(-5, 5)

                    particle.append(pos)
                    velocity.append(vel)

                self.swarm[idx] = particle
                self.velocities[idx] = velocity
                self.personal_best_scores[idx] = float('inf')

            # 策略2: 对所有粒子应用变异
            self.apply_mutation(self.mutation_rate)

        elif diversity > self.diversity_threshold_high:
            # 多样性过高，增强局部搜索
            for i in range(len(self.velocities)):
                for j in range(len(self.velocities[i])):
                    self.velocities[i][j] *= 0.5

    def apply_mutation(self, mutation_rate: float):
        """应用变异操作"""
        for i in range(self.swarm_size):
            if random.random() < mutation_rate:
                # 随机选择一个维度进行变异
                dim = random.randint(0, self.particle_size - 1)
                low, high = self.bounds[dim]

                if dim < 3:  # 连续变量
                    self.swarm[i][dim] = random.uniform(low, high)
                else:  # 整数变量
                    self.swarm[i][dim] = random.randint(low, high)

    def evaluate_fitness(self, particle: List[float]) -> float:
        """评估粒子适应度 (改进约束处理)"""
        try:
            # 计算约束违反
            violation_degree, violations_detail = self.calculate_constraint_violation(particle)

            if violation_degree > self.constraint_tolerance:
                # 自适应惩罚
                penalty = self.calculate_adaptive_penalty(
                    violation_degree, violations_detail,
                    len(self.best_scores_history)
                )
                return penalty

            # 检查储能比例约束
            pv_capacity, wind_capacity, storage_capacity = particle[:3]
            renewable_capacity = pv_capacity + wind_capacity
            if renewable_capacity > 0:
                actual_ratio = storage_capacity / renewable_capacity
                if actual_ratio < self.params.storage_min_ratio:
                    return self.params.constraint_penalty

            # 8760小时仿真
            hourly_results, annual_stats = simulate_8760_hours(
                particle, self.selected_types, self.pv_curve, self.wind_curve, self.params
            )

            # 检查电网约束
            total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
            if total_renewable > 0:
                grid_up_ratio = annual_stats['total_grid_up'] / total_renewable
                grid_down_ratio = annual_stats['total_grid_down'] / total_renewable
                curtailment_ratio = annual_stats['total_curtailed'] / total_renewable

                if grid_up_ratio > self.params.max_grid_up_ratio:
                    return self.params.constraint_penalty
                if grid_down_ratio > self.params.max_grid_down_ratio:
                    return self.params.constraint_penalty
                if curtailment_ratio > self.params.max_curtailment_ratio:
                    return self.params.constraint_penalty

            # 计算LCOH
            lcoh = calculate_lcoh(particle, self.selected_types, annual_stats, self.params)

            # 产氢量指数惩罚
            actual_h2 = annual_stats['total_h2_production']
            target_h2_nm3 = self.params.target_h2_production / 0.000089  # 吨转Nm³
            h2_deviation = abs(actual_h2 - target_h2_nm3) / target_h2_nm3

            if h2_deviation > self.params.h2_production_tolerance:
                excess_dev = h2_deviation - self.params.h2_production_tolerance
                penalty = self.params.h2_penalty_factor * (math.exp(excess_dev) - 1)
                lcoh += penalty

            return lcoh

        except Exception as e:
            print(f"适应度评估错误: {e}")
            return self.params.constraint_penalty

    def initialize_swarm(self):
        """初始化粒子群"""
        self.swarm = []
        self.velocities = []
        self.personal_best_positions = []
        self.personal_best_scores = []
        self.personal_improvement_history = [[] for _ in range(self.swarm_size)]

        for i in range(self.swarm_size):
            # 随机初始化粒子位置
            particle = []
            velocity = []

            for j, (low, high) in enumerate(self.bounds):
                if j < 3:  # 前3维是连续变量
                    pos = random.uniform(low, high)
                    vel = random.uniform(-abs(high-low)*0.1, abs(high-low)*0.1)
                else:  # 后面是整数变量
                    pos = random.randint(low, high)
                    vel = random.uniform(-5, 5)

                particle.append(pos)
                velocity.append(vel)

            self.swarm.append(particle)
            self.velocities.append(velocity)

            # 评估初始适应度
            score = self.evaluate_fitness(particle)
            self.personal_best_positions.append(particle[:])
            self.personal_best_scores.append(score)

            # 更新全局最优
            if score < self.global_best_score:
                self.global_best_score = score
                self.global_best_position = particle[:]

        print(f"✓ 粒子群初始化完成，初始最优LCOH: {self.global_best_score:.6f}")

    def update_particle(self, i: int, iteration: int):
        """更新粒子位置和速度 (自适应版本)"""
        # 计算自适应参数
        diversity = self.calculate_population_diversity()
        fitness_improvement = self.calculate_fitness_improvement()
        personal_success = self.calculate_personal_success_rate(i)

        # 自适应惯性权重和学习因子
        w = self.calculate_adaptive_inertia(iteration, diversity, fitness_improvement)
        c1, c2 = self.calculate_adaptive_learning_factors(iteration, personal_success)

        # 更新速度和位置
        for j in range(self.particle_size):
            # 更新速度
            r1 = random.random()
            r2 = random.random()

            cognitive_component = c1 * r1 * (self.personal_best_positions[i][j] - self.swarm[i][j])
            social_component = c2 * r2 * (self.global_best_position[j] - self.swarm[i][j])

            self.velocities[i][j] = w * self.velocities[i][j] + cognitive_component + social_component

            # 更新位置
            self.swarm[i][j] += self.velocities[i][j]

            # 边界处理
            low, high = self.bounds[j]
            if j < 3:  # 连续变量
                self.swarm[i][j] = max(low, min(high, self.swarm[i][j]))
            else:  # 整数变量
                self.swarm[i][j] = int(max(low, min(high, round(self.swarm[i][j]))))

        # 记录自适应参数 (用于调试)
        if i == 0:  # 只记录第一个粒子的参数
            self.diversity_history.append(diversity)
            if iteration % 10 == 0:
                print(f"  自适应参数: w={w:.3f}, c1={c1:.2f}, c2={c2:.2f}, diversity={diversity:.3f}")

    def optimize(self) -> Dict[str, Any]:
        """执行自适应PSO优化"""
        start_time = time.time()

        print(f"开始自适应PSO优化")
        print(f"粒子群规模: {self.swarm_size}, 最大迭代次数: {self.max_iterations}")

        # 初始化粒子群
        self.initialize_swarm()

        # 主优化循环
        for iteration in range(self.max_iterations):
            # 更新所有粒子
            for i in range(self.swarm_size):
                self.update_particle(i, iteration)

                # 评估新位置
                new_score = self.evaluate_fitness(self.swarm[i])

                # 更新个体最优
                improved = False
                if new_score < self.personal_best_scores[i]:
                    self.personal_best_scores[i] = new_score
                    self.personal_best_positions[i] = self.swarm[i][:]
                    improved = True

                # 记录个体改善历史
                self.personal_improvement_history[i].append(1 if improved else 0)
                if len(self.personal_improvement_history[i]) > 20:
                    self.personal_improvement_history[i].pop(0)

                # 更新全局最优
                if new_score < self.global_best_score:
                    self.global_best_score = new_score
                    self.global_best_position = self.swarm[i][:]

            # 记录历史
            self.best_scores_history.append(self.global_best_score)

            # 多样性维护 (每10代检查一次)
            if iteration % 10 == 0:
                diversity = self.calculate_population_diversity()
                self.maintain_diversity(diversity, iteration)

            # 输出进度
            if iteration % 10 == 0 or iteration == self.max_iterations - 1:
                self._print_progress(iteration)

            # 收敛判断
            if self._check_convergence():
                print(f"算法在第{iteration}代收敛")
                break

        end_time = time.time()
        calculation_time = end_time - start_time

        # 生成最终结果
        results = self._generate_results(calculation_time, iteration + 1)

        print(f"自适应PSO优化完成，耗时: {calculation_time:.2f}秒")

        return results

    def _print_progress(self, iteration: int):
        """打印优化进度"""
        if not self.global_best_position:
            return

        # 获取当前最优解的配置
        best_particle = self.global_best_position
        pv_cap = best_particle[0]
        wind_cap = best_particle[1]
        storage_cap = best_particle[2]
        elec_counts = best_particle[3:]

        # 计算电解槽总容量
        total_elec_cap = 0
        for i, elec_type in enumerate(self.selected_types):
            elec_spec = ELECTROLYZER_SPECS[elec_type]
            total_elec_cap += elec_counts[i] * elec_spec['rated_power']

        # 计算当前多样性
        diversity = self.calculate_population_diversity()

        print(f"迭代 {iteration:3d}: 最优LCOH={self.global_best_score:.6f}, "
              f"pv={pv_cap:.0f}, wind={wind_cap:.0f}, bat={storage_cap:.0f}, "
              f"alk={total_elec_cap:.0f}MW, diversity={diversity:.3f}")

    def _check_convergence(self) -> bool:
        """检查收敛条件"""
        if len(self.best_scores_history) < 10:
            return False

        # 检查最近10代的改善
        recent_improvement = self.best_scores_history[-10] - self.global_best_score
        if recent_improvement < self.convergence_threshold:
            self.convergence_count += 1
            if self.convergence_count >= 5:
                return True
        else:
            self.convergence_count = 0

        return False

    def _generate_results(self, calculation_time: float, iterations: int) -> Dict[str, Any]:
        """生成优化结果"""
        if not self.global_best_position:
            return {'error': 'No solution found'}

        # 解析最优解
        particle = self.global_best_position
        pv_capacity = particle[0]
        wind_capacity = particle[1]
        storage_capacity = particle[2]
        elec_counts = particle[3:]

        # 构建电解槽配置
        electrolyzer_config = []
        total_elec_capacity = 0
        for i, elec_type in enumerate(self.selected_types):
            count = int(elec_counts[i])
            if count > 0:
                elec_spec = ELECTROLYZER_SPECS[elec_type]
                capacity = count * elec_spec['rated_power']
                total_elec_capacity += capacity

                electrolyzer_config.append({
                    'type': elec_type,
                    'count': count,
                    'total_capacity': capacity
                })

        # 重新仿真获取详细结果
        try:
            hourly_results, annual_stats = simulate_8760_hours(
                particle, self.selected_types, self.pv_curve, self.wind_curve, self.params
            )
        except Exception as e:
            return {'error': f'Final simulation failed: {e}'}

        # 计算各种指标
        total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
        curtailment_ratio = annual_stats['total_curtailed'] / total_renewable if total_renewable > 0 else 0
        grid_up_ratio = annual_stats['total_grid_up'] / total_renewable if total_renewable > 0 else 0
        grid_down_ratio = annual_stats['total_grid_down'] / total_renewable if total_renewable > 0 else 0

        # 构建结果
        result = {
            # 基本配置
            'optimal_pv_capacity': pv_capacity,
            'optimal_wind_capacity': wind_capacity,
            'optimal_storage_capacity': storage_capacity,
            'optimal_electrolyzer_config': electrolyzer_config,
            'total_electrolyzer_capacity': total_elec_capacity,

            # 经济指标
            'optimal_lcoh': self.global_best_score,

            # 年度统计
            'annual_h2_production': annual_stats['total_h2_production'],
            'annual_h2_production_tons': annual_stats['total_h2_production'] * 0.000089,
            'curtailment_ratio': curtailment_ratio,
            'grid_up_ratio': grid_up_ratio,
            'grid_down_ratio': grid_down_ratio,

            # 自适应PSO特有信息
            'algorithm': 'AdaptivePSO',
            'adaptive_features': {
                'final_diversity': self.calculate_population_diversity(),
                'diversity_history': self.diversity_history[-20:],  # 最后20个记录
                'fitness_improvement_rate': self.calculate_fitness_improvement(),
                'convergence_iterations': len(self.best_scores_history),
                'diversity_maintenance_triggered': len([d for d in self.diversity_history if d < self.diversity_threshold_low])
            },

            # 优化信息
            'calculation_time': calculation_time,
            'optimization_iterations': iterations,
            'convergence_achieved': self.convergence_count >= 5,
            'best_scores_history': self.best_scores_history,
        }

        return result

    def get_adaptive_statistics(self) -> Dict[str, Any]:
        """获取自适应统计信息"""
        if not self.diversity_history:
            return {'error': 'No optimization history available'}

        return {
            'diversity_stats': {
                'min': min(self.diversity_history),
                'max': max(self.diversity_history),
                'avg': sum(self.diversity_history) / len(self.diversity_history),
                'final': self.diversity_history[-1] if self.diversity_history else 0
            },
            'maintenance_events': {
                'low_diversity_count': len([d for d in self.diversity_history if d < self.diversity_threshold_low]),
                'high_diversity_count': len([d for d in self.diversity_history if d > self.diversity_threshold_high]),
                'total_checks': len(self.diversity_history)
            },
            'convergence_info': {
                'convergence_count': self.convergence_count,
                'total_iterations': len(self.best_scores_history),
                'final_improvement_rate': self.calculate_fitness_improvement()
            }
        }
