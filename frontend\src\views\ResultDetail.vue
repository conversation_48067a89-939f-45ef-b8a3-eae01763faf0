<template>
  <div class="result-detail">
    <!-- <div class="page-header">
      <h2>风光储氢容量优化结果</h2>
    </div> -->
    
    <!-- 4个大卡片横向排列 -->
    <a-row :gutter="16" class="main-cards">
      
      <!-- 核心指标卡片 -->
      <a-col :span="6">
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon core-icon">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </div>
            <div class="card-title">核心指标</div>
          </div>
          <div class="card-content">
            <div class="data-row highlight">
              <span class="data-key">LCOH制氢成本</span>
              <span class="data-value primary">{{ Number(result?.optimal_lcoh || 0).toFixed(4) }} 元/Nm³</span>
            </div>
            <div class="data-row highlight">
              <span class="data-key">年产氢量</span>
              <span class="data-value primary">{{ getH2ProductionInTons() }} 吨</span>
            </div>
            <div class="data-row">
              <span class="data-key">总投资成本</span>
              <span class="data-value">{{ getTotalInvestmentInYi() }} 亿元</span>
            </div>
            <div class="data-row">
              <span class="data-key">制氢效率</span>
              <span class="data-value">{{ getH2Efficiency() }}%</span>
            </div>
            <!-- <div class="data-row">
              <span class="data-key">计算时间</span>
              <span class="data-value">{{ Number(result?.calculation_time || 0).toFixed(1) }}秒</span>
            </div>
            <div class="data-row">
              <span class="data-key">收敛状态</span>
              <span class="data-value">{{ result?.convergence_achieved ? '已收敛' : '未收敛' }}</span>
            </div> -->
          </div>
        </div>
      </a-col>

      <!-- 系统配置卡片 -->
      <a-col :span="6">
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon config-icon">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
              </svg>
            </div>
            <div class="card-title">系统配置</div>
          </div>
          <div class="card-content">
            <div class="data-row">
              <span class="data-key">光伏容量(MW)</span>
              <span class="data-value">{{ Number(result?.optimal_pv_capacity || 0).toFixed(1) }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">风机容量(MW)</span>
              <span class="data-value">{{ Number(result?.optimal_wind_capacity || 0).toFixed(1) }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">储能容量(MWh)</span>
              <span class="data-value">{{ Number(result?.optimal_storage_capacity || 0).toFixed(1) }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">电解槽总容量(MW)</span>
              <span class="data-value">{{ getTotalElectrolyzerCapacity() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">电解槽总数量(台)</span>
              <span class="data-value">{{ getTotalElectrolyzerCount() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">电解槽用电量(MWh)</span>
              <span class="data-value">{{ getElectrolyzerEnergyMWh() }}</span>
            </div>
          </div>
        </div>
      </a-col>

      <!-- 电网交互卡片 -->
      <a-col :span="6">
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon grid-icon">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                <path d="M8.28,5.45L6.5,4.55L7.76,2.1L9.54,3C10.29,2.7 11.1,2.5 11.94,2.33L12.4,0.5H15.6L16.06,2.33C16.9,2.5 17.71,2.7 18.46,3L20.24,2.1L21.5,4.55L19.72,5.45C19.91,6.32 19.91,7.23 19.72,8.1L21.5,9L20.24,11.45L18.46,10.55C17.71,10.85 16.9,11.05 16.06,11.22L15.6,13.05H12.4L11.94,11.22C11.1,11.05 10.29,10.85 9.54,10.55L7.76,11.45L6.5,9L8.28,8.1C8.09,7.23 8.09,6.32 8.28,5.45M14,6.25A1.75,1.75 0 0,0 12.25,8A1.75,1.75 0 0,0 14,9.75A1.75,1.75 0 0,0 15.75,8A1.75,1.75 0 0,0 14,6.25M22,16V18H19V23H17V18H14V16H17V11H19V16H22M9,18A3,3 0 0,0 6,21A3,3 0 0,0 9,24A3,3 0 0,0 12,21A3,3 0 0,0 9,18M9,22A1,1 0 0,1 8,21A1,1 0 0,1 9,20A1,1 0 0,1 10,21A1,1 0 0,1 9,22M3,14V16H0V14H3M4,18V20H1V18H4Z" />
              </svg>
            </div>
            <div class="card-title">电网交互</div>
          </div>
          <div class="card-content">
            <div class="data-row">
              <span class="data-key">上网电量(MWh)</span>
              <span class="data-value positive">{{ getGridUpMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">上网比例(%)</span>
              <span class="data-value">{{ getGridUpRatio() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">下网电量(MWh)</span>
              <span class="data-value negative">{{ getGridDownMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">下网比例(%)</span>
              <span class="data-value">{{ getGridDownRatio() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">弃电量(MWh)</span>
              <span class="data-value warning">{{ getCurtailedMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">弃电率(%)</span>
              <span class="data-value">{{ getCurtailmentRatio() }}</span>
            </div>
          </div>
        </div>
      </a-col>

      <!-- 发电与经济卡片 -->
      <a-col :span="6">
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon energy-icon">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                <path d="M7,2V13H10V22L17,10H13L17,2H7Z" />
              </svg>
            </div>
            <div class="card-title">发电与经济</div>
          </div>
          <div class="card-content">
            <div class="data-row">
              <span class="data-key">新能源发电(MWh)</span>
              <span class="data-value">{{ getRenewableEnergyMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">光伏发电(MWh)</span>
              <span class="data-value">{{ getPvEnergyMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">风机发电(MWh)</span>
              <span class="data-value">{{ getWindEnergyMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">储能充电(MWh)</span>
              <span class="data-value">{{ getStorageChargeMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">储能放电(MWh)</span>
              <span class="data-value">{{ getStorageDischargeMWh() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">年售电收入(万元)</span>
              <span class="data-value positive">{{ getAnnualIncome() }}</span>
            </div>
          </div>
        </div>
      </a-col>

    </a-row>

    <!-- 电解槽产品卡片和小时数据统计卡片 -->
    <a-row :gutter="16" class="detail-cards">
      <a-col :span="12">
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon h2-icon">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" />
              </svg>
            </div>
            <div class="card-title">电解槽产品</div>
          </div>
          <div class="card-content">
            <div class="electrolyzer-table">
              <div class="table-header">
                <span>型号</span>
                <span>额定(Nm³/h)</span>
                <span>电耗(kWh/Nm³)</span>
                <span>功率(MW)</span>
                <span>数量</span>
              </div>
              <div v-for="(elec, index) in result?.optimal_electrolyzer_config" :key="index" class="table-row">
                <span>{{ getElectrolyzerFullName(elec.type) }}</span>
                <span>{{ getElectrolyzerCapacity(elec.type) }}</span>
                <span>4.3</span>
                <span>{{ getElectrolyzerPower(elec.type) }}</span>
                <span>{{ elec.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="info-card">
          <div class="card-header">
            <div class="card-icon time-icon">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
              </svg>
            </div>
            <div class="card-title">小时数据统计</div>
          </div>
          <div class="card-content">
            <div class="data-row">
              <span class="data-key">新能源发电小时数(h)</span>
              <span class="data-value">{{ getRenewableGenerationHours() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">风机发电小时数(h)</span>
              <span class="data-value">{{ getWindGenerationHours() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">光伏发电小时数(h)</span>
              <span class="data-value">{{ getPvGenerationHours() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">新能源制氢小时数(h)</span>
              <span class="data-value">{{ getElectrolyzerRunningHours() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">电网制氢小时数(h)</span>
              <span class="data-value">{{ getGridH2Hours() }}</span>
            </div>
            <div class="data-row">
              <span class="data-key">储能制氢小时数(h)</span>
              <span class="data-value">{{ getStorageH2Hours() }}</span>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 功率曲线图表 -->
    <a-card title="8760小时功率曲线" size="small" class="chart-section">
      <div ref="powerChart" style="width: 100%; height: 500px;"></div>
    </a-card>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import { resultsApi } from '../api/results'

const route = useRoute()
const loading = ref(false)
const result = ref<any>(null)
const powerChart = ref<HTMLElement>()

// 数据处理方法
const getH2ProductionInTons = () => {
  // 年产氢量从Nm³转换为吨 (1吨氢气约等于11,126 Nm³)
  const h2Production = Number(result.value?.annual_h2_production || 0)
  return (h2Production / 11126).toFixed(2)
}

const getTotalInvestmentInYi = () => {
  const investment = Number(result.value?.total_capex || 0)
  return (investment / 100000000).toFixed(2)
}

const getTotalElectrolyzerCapacity = () => {
  const config = result.value?.optimal_electrolyzer_config || []
  const total = config.reduce((sum, elec) => sum + Number(elec.total_capacity || 0), 0)
  return total.toFixed(1)
}

const getTotalElectrolyzerCount = () => {
  const config = result.value?.optimal_electrolyzer_config || []
  return config.reduce((sum, elec) => sum + Number(elec.count || 0), 0)
}

const getElectrolyzerTypeName = (type: string) => {
  const typeNames = {
    'Hi1_PLus_1000': 'Hi1_Plus_1000',
    'G_2000': 'G_2000',
    'G_3000': 'G_3000',
    'P_200': 'P_200'
  }
  return typeNames[type] || type
}

const getElectrolyzerShortName = (type: string) => {
  const typeNames = {
    'Hi1_PLus_1000': 'Hi1+',
    'G_2000': 'G_2000',
    'G_3000': 'G_3000',
    'P_200': 'P_200'
  }
  return typeNames[type] || type
}

const getElectrolyzerFullName = (type: string) => {
  const typeNames = {
    'Hi1_PLus_1000': 'Hi1+ 1000',
    'G_2000': 'G 2000',
    'G_3000': 'G 3000',
    'P_200': 'P 200'
  }
  return typeNames[type] || type
}

const getElectrolyzerCapacity = (type: string) => {
  const capacities = {
    'Hi1_PLus_1000': '1000',
    'G_2000': '2000',
    'G_3000': '3000',
    'P_200': '200'
  }
  return capacities[type] || 'N/A'
}

const getElectrolyzerPower = (type: string) => {
  const powers = {
    'Hi1_PLus_1000': '5',
    'G_2000': '10',
    'G_3000': '15',
    'P_200': '1'
  }
  return powers[type] || 'N/A'
}

const getTotalRenewableCapacity = () => {
  const pv = Number(result.value?.optimal_pv_capacity || 0)
  const wind = Number(result.value?.optimal_wind_capacity || 0)
  return (pv + wind).toFixed(1)
}

const getRenewableEnergyMWh = () => {
  const pv = Number(result.value?.annual_pv_generation || 0)
  const wind = Number(result.value?.annual_wind_generation || 0)
  return (pv + wind).toFixed(1)
}

const getPvEnergyMWh = () => {
  const pv = Number(result.value?.annual_pv_generation || 0)
  return pv.toFixed(1)
}

const getWindEnergyMWh = () => {
  const wind = Number(result.value?.annual_wind_generation || 0)
  return wind.toFixed(1)
}

const getElectrolyzerEnergyMWh = () => {
  if (!result.value?.hourly_data) return '0.0'
  const totalEnergy = result.value.hourly_data
    .reduce((sum, item) => sum + Number(item.electrolyzer_total_power || 0), 0)
  return totalEnergy.toFixed(1)
}

const getH2Efficiency = () => {
  const electrolyzerEnergy = parseFloat(getElectrolyzerEnergyMWh())
  const renewableEnergy = parseFloat(getRenewableEnergyMWh())
  if (renewableEnergy === 0) return 'N/A'
  return ((electrolyzerEnergy / renewableEnergy) * 100).toFixed(2)
}

const getStorageChargeMWh = () => {
  if (!result.value?.hourly_data) return '0.0'
  const totalCharge = result.value.hourly_data
    .filter(item => Number(item.storage_power || 0) < 0)
    .reduce((sum, item) => sum + Math.abs(Number(item.storage_power || 0)), 0)
  return totalCharge.toFixed(1)
}

const getStorageDischargeMWh = () => {
  if (!result.value?.hourly_data) return '0.0'
  const totalDischarge = result.value.hourly_data
    .filter(item => Number(item.storage_power || 0) > 0)
    .reduce((sum, item) => sum + Number(item.storage_power || 0), 0)
  return totalDischarge.toFixed(1)
}

const getStorageEfficiency = () => {
  const charge = getStorageChargeMWh()
  const discharge = getStorageDischargeMWh()
  const total = parseFloat(charge) + parseFloat(discharge)
  if (total === 0) return 'N/A'
  return ((parseFloat(charge) / total) * 100).toFixed(2)
}

const getGridUpMWh = () => {
  const gridUp = Number(result.value?.annual_grid_up || 0)
  return gridUp.toFixed(1)
}

const getGridUpRatio = () => {
  const ratio = Number(result.value?.grid_up_ratio || 0)
  return (ratio * 100).toFixed(2)
}

const getGridDownMWh = () => {
  const gridDown = Number(result.value?.annual_grid_down || 0)
  return gridDown.toFixed(1)
}

const getGridDownRatio = () => {
  const ratio = Number(result.value?.grid_down_ratio || 0)
  return (ratio * 100).toFixed(2)
}

const getCurtailedMWh = () => {
  const curtailed = Number(result.value?.annual_curtailed || 0)
  return curtailed.toFixed(1)
}

const getCurtailmentRatio = () => {
  const ratio = Number(result.value?.curtailment_ratio || 0)
  return (ratio * 100).toFixed(2)
}

const getEnergyUtilization = () => {
  const renewableEnergy = parseFloat(getRenewableEnergyMWh())
  const electrolyzerEnergy = parseFloat(getElectrolyzerEnergyMWh())
  const totalEnergy = renewableEnergy + electrolyzerEnergy
  if (totalEnergy === 0) return 'N/A'
  return ((renewableEnergy / totalEnergy) * 100).toFixed(2)
}

const getAnnualIncome = () => {
  const income = Number(result.value?.annual_income || 0)
  return (income / 10000).toFixed(1) // 转换为万元
}

const getPvCapex = () => {
  const pvCapacity = Number(result.value?.optimal_pv_capacity || 0) // MW
  const pvCost = 2.8 // 元/W, 2.8万元/MW
  return (pvCapacity * pvCost * 100).toFixed(0) // 转换为万元
}

const getWindCapex = () => {
  const windCapacity = Number(result.value?.optimal_wind_capacity || 0) // MW
  const windCost = 3.2 // 元/W, 3.2万元/MW
  return (windCapacity * windCost * 100).toFixed(0) // 转换为万元
}

const getStorageCapex = () => {
  const storageCapacity = Number(result.value?.optimal_storage_capacity || 0) // MWh
  const storageCost = 0.7 // 元/Wh, 70万元/MWh
  return (storageCapacity * 70).toFixed(0) // 转换为万元
}

const getElectrolyzerCapex = () => {
  const electrolyzerTypes = result.value?.optimal_electrolyzer_config || []
  const totalCapex = electrolyzerTypes.reduce((sum, elec) => {
    const count = Number(elec.count || 0)
    // 根据电解槽类型获取EPC价格
    let epcPrice = 0
    if (elec.type === 'Hi1_PLus_1000') epcPrice = 850 // 万元
    else if (elec.type === 'G_2000') epcPrice = 1200 // 万元
    else if (elec.type === 'G_3000') epcPrice = 1600 // 万元
    else if (elec.type === 'P_200') epcPrice = 170 // 万元
    return sum + (count * epcPrice)
  }, 0)
  return totalCapex.toFixed(0)
}

const getBasicOpex = () => {
  const opex = Number(result.value?.basic_opex || 0)
  return (opex / 10000).toFixed(1) // 转换为万元
}

const getWaterCost = () => {
  const cost = Number(result.value?.water_cost || 0)
  return (cost / 10000).toFixed(1) // 转换为万元
}

const getGridPurchaseCost = () => {
  const cost = Number(result.value?.grid_purchase_cost || 0)
  return (cost / 10000).toFixed(1) // 转换为万元
}

const getFirstYearOpex = () => {
  const opex = Number(result.value?.first_year_opex_total || 0)
  return (opex / 10000).toFixed(1) // 转换为万元
}

// 小时数统计计算函数 - 使用等效小时数计算
const getRenewableGenerationHours = () => {
  if (!result.value?.hourly_data) return 0
  const windCapacity = Number(result.value?.optimal_wind_capacity || 0)
  const pvCapacity = Number(result.value?.optimal_pv_capacity || 0)
  const totalCapacity = windCapacity + pvCapacity
  
  if (totalCapacity === 0) return 0
  
  const totalGeneration = result.value.hourly_data
    .reduce((sum, item) => sum + Number(item.pv_power || 0) + Number(item.wind_power || 0), 0)
  
  return (totalGeneration / totalCapacity).toFixed(0)
}

const getWindGenerationHours = () => {
  if (!result.value?.hourly_data) return 0
  const windCapacity = Number(result.value?.optimal_wind_capacity || 0)
  
  if (windCapacity === 0) return 0
  
  const windGeneration = result.value.hourly_data
    .reduce((sum, item) => sum + Number(item.wind_power || 0), 0)
  
  return (windGeneration / windCapacity).toFixed(0)
}

const getElectrolyzerRunningHours = () => {
  if (!result.value?.hourly_data) return 0
  const electrolyzerCapacity = Number(getTotalElectrolyzerCapacity())
  
  if (electrolyzerCapacity === 0) return 0
  
  // 计算可再生能源制氢用电量
  const renewableH2Electricity = result.value.hourly_data
    .reduce((sum, item) => {
      const electrolyzerPower = Number(item.electrolyzer_total_power || 0)
      const gridPower = Number(item.grid_power || 0)
      // 如果电网功率为负（下网），则从电解槽功率中减去下网部分
      const renewablePower = gridPower < 0 ? electrolyzerPower + gridPower : electrolyzerPower
      return sum + Math.max(0, renewablePower)
    }, 0)
  
  return (renewableH2Electricity / electrolyzerCapacity).toFixed(0)
}

const getPvGenerationHours = () => {
  if (!result.value?.hourly_data) return 0
  const pvCapacity = Number(result.value?.optimal_pv_capacity || 0)
  
  if (pvCapacity === 0) return 0
  
  const pvGeneration = result.value.hourly_data
    .reduce((sum, item) => sum + Number(item.pv_power || 0), 0)
  
  return (pvGeneration / pvCapacity).toFixed(0)
}

const getStorageChargeHours = () => {
  if (!result.value?.hourly_data) return 0
  const electrolyzerCapacity = Number(getTotalElectrolyzerCapacity())
  
  if (electrolyzerCapacity === 0) return 0
  
  // 计算储能制氢用电量（储能放电用于制氢）
  const storageH2Electricity = result.value.hourly_data
    .reduce((sum, item) => {
      const storagePower = Number(item.storage_power || 0)
      const electrolyzerPower = Number(item.electrolyzer_total_power || 0)
      const gridPower = Number(item.grid_power || 0)
      
      // 如果储能放电且电解槽运行，计算储能贡献的制氢电量
      if (storagePower > 0 && electrolyzerPower > 0) {
        // 储能放电量不能超过电解槽需求量
        const renewableAndGrid = Math.max(0, gridPower) // 只考虑下网电量
        const maxStorageForH2 = Math.max(0, electrolyzerPower - renewableAndGrid)
        return sum + Math.min(storagePower, maxStorageForH2)
      }
      return sum
    }, 0)
  
  return (storageH2Electricity / electrolyzerCapacity).toFixed(0)
}

const getGridH2Hours = () => {
  if (!result.value?.hourly_data) return 0
  const electrolyzerCapacity = Number(getTotalElectrolyzerCapacity())
  
  if (electrolyzerCapacity === 0) return 0
  
  // 计算电网制氢用电量（电网下网用于制氢）
  const gridH2Electricity = result.value.hourly_data
    .reduce((sum, item) => {
      const gridPower = Number(item.grid_power || 0)
      const electrolyzerPower = Number(item.electrolyzer_total_power || 0)
      
      // 如果电网下网且电解槽运行，计算电网贡献的制氢电量
      if (gridPower < 0 && electrolyzerPower > 0) {
        // 下网电量不能超过电解槽需求量
        return sum + Math.min(Math.abs(gridPower), electrolyzerPower)
      }
      return sum
    }, 0)
  
  return (gridH2Electricity / electrolyzerCapacity).toFixed(0)
}

const getStorageH2Hours = () => {
  // 重命名原来的函数内容，因为它计算的就是储能制氢小时数
  return getStorageChargeHours()
}

const fetchResult = async () => {
  loading.value = true
  try {
    const resultId = route.params.id as string
    const data = await resultsApi.getResult(resultId)
    result.value = data
    
    // 确保 DOM 元素完全渲染后再初始化图表
    await nextTick()
    
    // 等待更长时间确保DOM完全渲染
    setTimeout(() => {
      if (powerChart.value) {
        renderPowerChart()
      } else {
        // 如果还是找不到元素，再等一会儿
        setTimeout(() => {
          renderPowerChart()
        }, 500)
      }
    }, 200)
  } catch (error) {
    console.error('获取结果失败:', error)
    message.error('获取结果失败')
  } finally {
    loading.value = false
  }
}

const renderPowerChart = (retryCount = 0) => {
  console.log('开始渲染图表...', 'retry:', retryCount)
  console.log('powerChart.value:', powerChart.value)
  console.log('result.value?.hourly_data长度:', result.value?.hourly_data?.length)
  
  if (!powerChart.value) {
    if (retryCount < 3) {
      console.log('DOM 元素未找到，等待重试...', retryCount + 1)
      setTimeout(() => {
        renderPowerChart(retryCount + 1)
      }, 300)
      return
    } else {
      console.error('DOM 元素未找到，已重试3次，放弃渲染')
      return
    }
  }
  
  if (!result.value?.hourly_data) {
    console.error('数据为空')
    return
  }
  
  try {
    const chartInstance = echarts.init(powerChart.value)
    console.log('ECharts 实例创建成功')
    
    const hourlyData = result.value.hourly_data
    
    // 准备基础数据
    const hours = hourlyData.map((_, index) => index + 1)
    const pvPower = hourlyData.map(item => item.pv_power || 0)
    const windPower = hourlyData.map(item => item.wind_power || 0)
    const renewablePower = hourlyData.map(item => item.renewable_power || 0)
    const storagePower = hourlyData.map(item => item.storage_power || 0)
    const gridPower = hourlyData.map(item => item.grid_power || 0)
    const electrolyzerTotalPower = hourlyData.map(item => item.electrolyzer_total_power || 0)
    const storageSoc = hourlyData.map(item => (item.storage_soc || 0) * 100) // 转换为百分比

    // 定义统一的颜色主题 - 更加协调的配色方案
    const colorTheme = {
      // 主功率曲线颜色 - 采用清晰明快的配色
      renewable: '#1890ff',      // 风光一体 - 主蓝色
      pv: '#faad14',            // 光伏 - 金黄色
      wind: '#13c2c2',          // 风机 - 青色
      storage: '#722ed1',        // 储能 - 紫色
      grid: '#f5222d',          // 电网 - 红色
      electrolyzer: '#52c41a',   // 电解槽总功率 - 绿色
      soc: '#eb2f96',           // SOC - 粉红色
      
      // 电解槽单台颜色系列 - 按类型分组，每组内渐变
      electrolyzerTypes: {
        'Hi1_PLus_1000': ['#95de64', '#73d13d', '#52c41a', '#389e0d', '#237804'], // 绿色系
        'G_2000': ['#ffd591', '#ffb347', '#fa8c16', '#d46b08', '#ad4e00'],       // 橙色系  
        'G_3000': ['#91d5ff', '#69c0ff', '#1890ff', '#096dd9', '#0050b3'],       // 蓝色系
        'P_200': ['#d3adf7', '#b37feb', '#722ed1', '#531dab', '#391085']         // 紫色系
      }
    }

    // 准备单台电解槽功率数据
    const electrolyzerTypes = result.value?.optimal_electrolyzer_config || []
    const singleElectrolyzerSeries: any[] = []
    const singleElectrolyzerLegends: string[] = []
    
    // 为每种电解槽类型的每台设备创建独立系列
    electrolyzerTypes.forEach((elecConfig, typeIndex) => {
      const electrolyzerCount = elecConfig.count || 0
      const typeName = getElectrolyzerTypeName(elecConfig.type)
      const typeColors = colorTheme.electrolyzerTypes[elecConfig.type] || 
                        ['#8c8c8c', '#595959', '#434343', '#262626', '#1f1f1f']
      
      // 为每台电解槽创建一个独立的系列
      for (let i = 0; i < electrolyzerCount; i++) {
        const machineIndex = i + 1
        const legendName = `${typeName}-${machineIndex}号机`
        
        const singleElectrolyzerPower = hourlyData.map(item => {
          const electrolyzerDetail = item.electrolyzer_details?.find(detail => detail.type === elecConfig.type)
          const totalPower = electrolyzerDetail?.total_power || 0
          const runningCount = electrolyzerDetail?.running_count || 0
          
          // 计算单台功率：总功率除以运行台数
          if (runningCount > 0 && i < runningCount) {
            return totalPower / runningCount
          }
          return 0
        })
        
        singleElectrolyzerSeries.push({
          name: legendName,
          type: 'bar',
          stack: 'single_electrolyzer',
          data: singleElectrolyzerPower,
          itemStyle: { 
            color: typeColors[i % typeColors.length],
            opacity: 0.85,
            borderWidth: 0
          },
          emphasis: { 
            focus: 'series',
            itemStyle: {
              opacity: 1,
              borderWidth: 0,
              borderColor: '#fff'
            }
          },
          barWidth: '60%'
        })
        
        singleElectrolyzerLegends.push(legendName)
      }
    })

    // 构建所有系列数据
    const allSeries = [
      {
        name: '光伏功率',
        type: 'line',
        data: pvPower,
        lineStyle: { 
          color: colorTheme.pv, 
          width: 1,
          type: 'solid'
        },
        itemStyle: { color: colorTheme.pv },
        symbol: 'none',
        smooth: true
      },
      {
        name: '风机功率', 
        type: 'line',
        data: windPower,
        lineStyle: { 
          color: colorTheme.wind, 
          width: 1,
          type: 'solid'
        },
        itemStyle: { color: colorTheme.wind },
        symbol: 'none',
        smooth: true
      },
      {
        name: '风光一体',
        type: 'line',
        data: renewablePower,
        lineStyle: { 
          color: colorTheme.renewable, 
          width: 1,
          type: 'solid'
        },
        itemStyle: { color: colorTheme.renewable },
        symbol: 'none',
        smooth: true,
        emphasis: { focus: 'series' }
      },
      {
        name: '储能功率',
        type: 'line',
        data: storagePower,
        lineStyle: { 
          color: colorTheme.storage, 
          width: 1,
          type: 'solid'
        },
        itemStyle: { color: colorTheme.storage },
        symbol: 'none',
        smooth: true
      },
      {
        name: '电网功率',
        type: 'line',
        data: gridPower,
        lineStyle: { 
          color: colorTheme.grid, 
          width: 1,
          type: 'solid'
        },
        itemStyle: { color: colorTheme.grid },
        symbol: 'none',
        smooth: true
      },
      {
        name: '电解槽总功率',
        type: 'line',
        data: electrolyzerTotalPower,
        lineStyle: { 
          color: colorTheme.electrolyzer, 
          width: 1,
          type: 'solid'
        },
        itemStyle: { color: colorTheme.electrolyzer },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: colorTheme.electrolyzer + '80' // 50% 透明度
            }, {
              offset: 1, color: colorTheme.electrolyzer + '20' // 12% 透明度
            }]
          }
        },
        symbol: 'none',
        smooth: true,
        emphasis: { 
          focus: 'series',
          lineStyle: { width: 1 }
        }
      },
      ...singleElectrolyzerSeries, // 添加所有单台电解槽系列
      {
        name: '储能SOC',
        type: 'line',
        yAxisIndex: 1,
        data: storageSoc,
        lineStyle: { 
          color: colorTheme.soc, 
          width: 1, 
          type: 'dashed' 
        },
        itemStyle: { color: colorTheme.soc },
        symbol: 'none',
        smooth: true
      }
    ]

    // 构建完整的图例数据 - 调整顺序，电解槽总功率靠前
    const basicLegends = ['光伏功率', '风机功率', '风光一体', '储能功率', '电网功率']
    const mainLegends = ['电解槽总功率'] // 移到前面
    const endLegends = ['储能SOC']
    const legendData = [...basicLegends, ...mainLegends,...endLegends, ...singleElectrolyzerLegends]
    
    // 默认选中项：只选中风光一体、储能功率、电网功率、电解槽总功率
    const defaultSelected: Record<string, boolean> = {}
    legendData.forEach(name => {
      if (['风光一体', '储能功率', '电网功率', '电解槽总功率'].includes(name)) {
        defaultSelected[name] = true
      } else {
        defaultSelected[name] = false
      }
    })

    const option = {
      title: {
        text: '风光储氢系统功率曲线',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#262626'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          lineStyle: {
            color: '#999',
            width: 1,
            type: 'dashed'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ddd',
        borderWidth: 0,
        textStyle: {
          color: '#333'
        },
        formatter: function(params) {
          let result = `<div style="font-weight: bold; margin-bottom: 5px;">时刻: ${params[0].axisValue}h</div>`
          params.forEach(param => {
            const value = param.seriesName === '储能SOC' 
              ? `${param.value.toFixed(1)}%` 
              : `${param.value.toFixed(2)} MW`
            result += `<div style="margin: 2px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; margin-right: 5px; border-radius: 2px;"></span>
              ${param.seriesName}: ${value}
            </div>`
          })
          return result
        }
      },
      legend: {
        data: legendData,
        top: 35,
        selected: defaultSelected,
        type: 'scroll',
        pageButtonItemGap: 5,
        pageIconSize: 12,
        itemGap: 15,
        textStyle: {
          fontSize: 12,
          color: '#333'
        }
      },
      grid: {
        left: '3%',
        right: '5%',
        bottom: '10%',
        containLabel: true,
        top: 90,
        borderColor: '#ddd'
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none',
            title: {
              zoom: '区域缩放',
              back: '缩放还原'
            }
          },
          restore: {
            title: '还原'
          },
          saveAsImage: {
            title: '保存为图片',
            name: '风光储氢系统功率曲线'
          }
        },
        iconStyle: {
          borderColor: '#999'
        },
        emphasis: {
          iconStyle: {
            borderColor: '#1890ff'
          }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: hours,
        name: '时间 (小时)',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 14,
          color: '#333'
        },
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '功率 (MW)',
          position: 'left',
          nameTextStyle: {
            fontSize: 14,
            color: '#333'
          },
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        {
          type: 'value',
          name: 'SOC (%)',
          position: 'right',
          min: 0,
          max: 100,
          nameTextStyle: {
            fontSize: 14,
            color: '#333'
          },
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 12
          }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 10
        },
        {
          start: 0,
          end: 10,
          height: 50,
          bottom: 20,
          handleStyle: {
            color: '#1890ff'
          },
          textStyle: {
            color: '#333'
          }
        }
      ],
      series: allSeries
    }

    chartInstance.setOption(option)
    console.log('图表渲染成功')
    console.log('图例数据:', legendData)
    console.log('默认选中:', defaultSelected)
    
    // 响应式调整
    window.addEventListener('resize', () => {
      chartInstance.resize()
    })
  } catch (error) {
    console.error('图表渲染失败:', error)
  }
}

onMounted(() => {
  fetchResult()
})
</script>

<style scoped>
.result-detail {
  padding: 24px;
  min-height: calc(100vh - 64px);
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  background-attachment: fixed;
}

.page-header {
  text-align: center;
  margin-bottom: 24px;
}

.page-header h2 {
  color: white;
  font-size: 28px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.main-cards {
  margin-bottom: 20px;
}

.detail-cards {
  margin-bottom: 20px;
}

.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  height: 350px;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  display: none;
}

.core-icon {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.config-icon {
  background: linear-gradient(45deg, #52c41a, #389e0d);
}

.grid-icon {
  background: linear-gradient(45deg, #fa8c16, #d46b08);
}

.energy-icon {
  background: linear-gradient(45deg, #722ed1, #531dab);
}

.h2-icon {
  background: linear-gradient(45deg, #13c2c2, #08979c);
}

.time-icon {
  background: linear-gradient(45deg, #1890ff, #096dd9);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.card-content {
  padding: 20px;
  height: calc(100% - 73px);
  overflow-y: auto;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-row:last-child {
  border-bottom: none;
}

.data-row.highlight {
  background: rgba(24, 144, 255, 0.05);
  padding: 12px 16px;
  margin: 0 -16px 8px;
  border-radius: 6px;
  border-bottom: none;
}

.data-key {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.data-value {
  font-size: 14px;
  color: #262626;
  font-weight: 600;
  text-align: right;
}

.data-value.primary {
  color: #1890ff;
  font-size: 16px;
}

.data-value.positive {
  color: #52c41a;
}

.data-value.negative {
  color: #ff4d4f;
}

.data-value.warning {
  color: #fa8c16;
}

/* 电解槽表格样式 */
.electrolyzer-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 0.6fr;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
  font-size: 12px;
  color: #666;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 0.6fr;
  gap: 8px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
  color: #262626;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f8f9fa;
}

/* 图表区域 */
.chart-section {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.chart-section :deep(.ant-card-head) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.chart-section :deep(.ant-card-head-title) {
  font-weight: 600;
  color: #262626;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: white;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: rgba(255,255,255,0.8);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-cards .ant-col {
    span: 12;
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .result-detail {
    padding: 16px;
  }
  
  .main-cards .ant-col,
  .detail-cards .ant-col {
    span: 24;
    margin-bottom: 16px;
  }
  
  .info-card {
    height: 200px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr;
    font-size: 11px;
  }
}
</style>