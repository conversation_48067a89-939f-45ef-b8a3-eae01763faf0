<template>
  <div id="app">
    <a-layout style="min-height: 100vh">
      <!-- <a-layout-header style="background: #fff; padding: 0 50px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)">
        <div style="display: flex; align-items: center; height: 64px">
          <h1 style="margin: 0; color: #1890ff">风光储氢容量优化</h1>
        </div>
      </a-layout-header> -->
      
      <a-layout-content style="padding: 24px">
        <router-view />
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
// Vue 3 Composition API
</script>

<style>
#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
</style> 