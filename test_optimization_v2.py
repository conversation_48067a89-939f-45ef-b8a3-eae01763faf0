#!/usr/bin/env python3
"""
优化版PSO算法测试脚本
特点：
1. 0.1MW步长的离散化搜索
2. 缓存机制避免重复计算
3. 更高的计算效率
"""

import sys
import os
import json
from datetime import datetime

sys.path.append('./backend')

from models.parameters import OptimizationParameters
from algorithms.pso_optimized import OptimizedPSO


def save_result_to_output(result, params, suffix="_optimized"):
    """将结果保存到output文件夹（复用公共逻辑）"""
    
    # 确保output目录存在
    output_dir = "./output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成结果ID
    existing_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]
    result_id = str(len(existing_files) + 1)
    
    # 添加计算信息
    calculation_info = {
        "result_id": result_id,
        "calculation_time": datetime.now().isoformat(),
        "parameters": params.model_dump(),
        "selected_electrolyzer_types": params.selected_electrolyzer_types,
        "optimization_method": "OptimizedPSO",
        "features": ["0.1MW_step_size", "caching_mechanism", "discrete_search"]
    }
    
    # 合并结果
    final_result = {**result, **calculation_info}
    
    # 保存结果到文件
    result_file = os.path.join(output_dir, f"{result_id}.json")
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✓ 结果已保存到: {result_file}")
    print(f"✓ 结果ID: {result_id}")
    print(f"✓ 前端可通过 GET /api/results/{result_id} 获取此结果")
    
    return result_id


def test_optimized_pso():
    """测试优化版PSO算法"""
    
    print("=== 优化版PSO算法测试 ===")
    
    # 创建优化参数配置（与原版本相同的参数）
    params = OptimizationParameters(
        # ==================== 项目经济参数 ====================
        project_years=25,                      # 项目运营周期 (年)
        target_h2_production=12000,             # 目标年产氢量 (吨) - 合理目标
        loan_years=15,                         # 贷款年限 (年)
        loan_ratio=0.7,                        # 贷款比例 (70%贷款，30%自有资金)
        loan_rate=0.031,                       # 贷款利率 (3.1%)
        discount_rate=0.06,                    # 设备折现率 (6%)

        # ==================== 光伏设备参数 ====================
        pv_capacity_min=0,                    # 光伏装机容量下限 (MW)
        pv_capacity_max=200,                     # 光伏装机容量上限 (MW)
        pv_first_year_degradation=0.01,        # 光伏首年衰减率 (1%)
        pv_annual_degradation=0.0035,          # 光伏年衰减率 (0.35%)
        pv_epc_cost=2.8,                       # 光伏EPC投资成本 (元/W)
        pv_om_cost=0.05,                       # 光伏运维成本 (元/W/年)
        
        # ==================== 风机设备参数 ====================
        wind_capacity_min=0,                   # 风机装机容量下限 (MW)
        wind_capacity_max=200,                   # 风机装机容量上限 (MW)
        wind_epc_cost=3.2,                     # 风机EPC投资成本 (元/W)
        wind_om_cost=0.055,                      # 风机运维成本 (元/W/年)
        
        # ==================== 电网交互约束 ====================
        max_grid_down_ratio=0.03,               # 年最大下网比例 (3%)
        max_grid_up_ratio=0.03,                 # 年最大上网比例 (3%)
        grid_purchase_price=0.45,              # 网购电价 (元/kWh)
        green_electricity_price=0.2829,         # 绿电上网价格 (元/kWh)
        
        # ==================== 储能系统参数 ====================
        storage_capacity_min=0,               # 储能装机容量下限 (MWh)
        storage_capacity_max=100,             # 储能装机容量上限 (MWh)
        storage_min_ratio=0.1,                # 储能占新能源容量的最小比例 (10%)
        storage_epc_cost=1.5,                 # 储能EPC投资成本 (元/Wh)
        storage_om_cost=0.06,                 # 储能运维成本 (元/Wh/年)
        charge_efficiency=0.95,                # 储能充电效率 (95%)
        discharge_efficiency=0.95,             # 储能放电效率 (95%)
        charge_discharge_rate=0.5,             # 储能充放电倍率 (0.5C)
        initial_soc=0.5,                       # 储能初始SOC (50%)
        soc_min=0.1,                           # 储能SOC下限 (10%)
        soc_max=0.9,                           # 储能SOC上限 (90%)
        
        # ==================== 电解槽配置参数 ====================
        selected_electrolyzer_types=["Hi1_PLus_1000", "G_2000", "G_3000"],
        electrolyzer_capacity_min=0,          # 电解槽总装机容量下限 (MW)
        electrolyzer_capacity_max=200,         # 电解槽总装机容量上限 (MW)
        water_price=4.38,                      # 用水价格 (元/吨)
        water_consumption=1.4,                 # 制氢耗水量 (L/Nm³)
        om_ratio=0.03,                         # 电解槽运维比例 (3%投资额)
        
        # ==================== PSO算法参数 ====================
        swarm_size=50,                         # 粒子群规模 (50个粒子)
        max_iterations=80,                    # 最大迭代次数 (80代) - 缓存后可以减少迭代次数
        w_start=0.9,                           # 初始惯性权重 (0.9)
        w_end=0.4,                             # 终止惯性权重 (0.4)
        c1=2.0,                                # 认知学习因子
        c2=2.0,                                # 社会学习因子
        convergence_threshold=1e-7,            # 收敛阈值
        
        # ==================== 约束和惩罚参数 ====================
        constraint_penalty=1e6,                # 约束违反惩罚值
        
        # ==================== 产氢量约束参数 (指数惩罚) ====================  
        h2_production_tolerance=0.02,          # 产氢量偏差容忍度 (2%)
        h2_penalty_factor=50.0,                # 产氢量指数惩罚强度
        
        # ==================== 弃电率硬约束 ====================
        max_curtailment_ratio=0.05,             # 最大允许弃电率 (5%)
    )
    
    # 输出关键参数信息
    print(f"✓ 优化特性: 0.1MW步长 + 缓存机制")
    print(f"✓ 光伏容量范围: {params.pv_capacity_min}-{params.pv_capacity_max} MW")
    print(f"✓ 风机容量范围: {params.wind_capacity_min}-{params.wind_capacity_max} MW")
    print(f"✓ 储能容量范围: {params.storage_capacity_min}-{params.storage_capacity_max} MWh")
    print(f"✓ 储能最小比例: ≥{params.storage_min_ratio*100}% (硬约束)")
    print(f"✓ 电解槽容量范围: {params.electrolyzer_capacity_min}-{params.electrolyzer_capacity_max} MW")
    print(f"✓ 选择的电解槽类型: {params.selected_electrolyzer_types}")
    print(f"✓ 目标产氢量: {params.target_h2_production} 吨/年")
    print(f"✓ 电网约束: 上网≤{params.max_grid_up_ratio*100}%, 下网≤{params.max_grid_down_ratio*100}%")
    print(f"✓ 产氢量惩罚: 容忍度{params.h2_production_tolerance*100}%, 惩罚强度{params.h2_penalty_factor}")
    print(f"✓ 弃电率硬约束: ≤{params.max_curtailment_ratio*100}% (超出直接拒绝)")
    print(f"✓ 粒子群规模: {params.swarm_size}, 最大迭代次数: {params.max_iterations}")
    print(f"✓ 约束机制: 弃电率硬约束 + 产氢量指数惩罚 + 缓存优化")
    
    try:
        # 创建优化版PSO优化器
        pso = OptimizedPSO(params.selected_electrolyzer_types, params)
        
        print(f"\n✓ 优化版PSO优化器创建成功")
        print(f"✓ 粒子维度: {pso.particle_size}")
        print(f"✓ 边界约束: {pso.bounds}")
        print(f"✓ 缓存机制: 已启用")
        
        # 执行优化
        print(f"\n开始优化计算...")
        result = pso.optimize()
        
        # 输出关键结果
        print(f"\n=== 最优解详细成本分解 ===")
        print(f"✓ 投资成本: {result['total_capex']/1e8:.2f} 亿元")
        print(f"✓ 年度运维成本: {result['total_opex']/1e4:.1f} 万元/年")
        print(f"✓ 年售电收入: {result['annual_income']/1e4:.1f} 万元/年")
        print(f"✓ 年制氢量: {result['annual_h2_production']*0.000089:.0f} 吨")
        print(f"✓ 年光伏发电: {result['annual_pv_generation']:.1f} MWh")
        print(f"✓ 年风机发电: {result['annual_wind_generation']:.1f} MWh")
        print(f"✓ 年上网电量: {result['annual_grid_up']:.1f} MWh")
        print(f"✓ 年下网电量: {result['annual_grid_down']:.1f} MWh")
        
        print(f"\n=== 优化结果 ===")
        print(f"✓ 最优LCOH: {result['optimal_lcoh']:.6f} 元/Nm³")
        print(f"✓ 光伏容量: {result['optimal_pv_capacity']:.2f} MW")
        print(f"✓ 风机容量: {result['optimal_wind_capacity']:.2f} MW") 
        print(f"✓ 储能容量: {result['optimal_storage_capacity']:.2f} MWh")
        print(f"✓ 年产氢量: {result['annual_h2_production']*0.000089:.2f} 吨")
        print(f"✓ 计算时间: {result['calculation_time']:.2f} 秒")
        print(f"✓ 收敛状态: {'是' if result['convergence_achieved'] else '否'}")
        
        print(f"\n=== 缓存优化效果 ===")
        print(f"✓ 计算时间: {result['calculation_time']:.2f}秒")
        print(f"✓ 迭代次数: {result['optimization_iterations']}代")
        print(f"✓ 收敛状态: {'是' if result['convergence_achieved'] else '否'}")
        print(f"✓ 0.1MW步长离散化: 已启用")
        print(f"✓ 缓存机制: 已启用")
        
        print(f"\n电解槽配置:")
        for elec in result['optimal_electrolyzer_config']:
            # 将枚举类型转换为字符串，去掉前缀
            elec_type_str = str(elec['type']).replace('ElectrolyzerType.', '')
            print(f"• {elec_type_str}: {elec['count']}台 ({elec['total_capacity']:.1f}MW)")
        
        print(f"\n电网约束检查:")
        grid_up_ratio = result['grid_up_ratio'] * 100
        grid_down_ratio = result['grid_down_ratio'] * 100
        print(f"• 上网比例: {grid_up_ratio:.2f}% (限制≤{params.max_grid_up_ratio*100}%)")
        print(f"• 下网比例: {grid_down_ratio:.2f}% (限制≤{params.max_grid_down_ratio*100}%)")
        print(f"• 弃电率: {result['curtailment_ratio']*100:.2f}%")
        
        # 约束满足性检查
        constraints_ok = True
        if grid_up_ratio > params.max_grid_up_ratio * 100:
            print(f"⚠️  上网比例超限！")
            constraints_ok = False
        if grid_down_ratio > params.max_grid_down_ratio * 100:
            print(f"⚠️  下网比例超限！")
            constraints_ok = False
        if constraints_ok:
            print(f"✅ 电网约束满足")
        
        # 产氢量约束检查
        actual_h2_tons = result['annual_h2_production'] * 0.000089
        h2_deviation_rate = abs(actual_h2_tons - params.target_h2_production) / params.target_h2_production
        print(f"\n产氢量约束检查:")
        
        if h2_deviation_rate <= params.h2_production_tolerance:
            print(f"✅ 产氢量约束满足 (偏差在容忍范围内)")
        else:
            excess_dev = h2_deviation_rate - params.h2_production_tolerance
            import math
            penalty_value = params.h2_penalty_factor * (math.exp(excess_dev) - 1)
            print(f"⚠️  产氢量约束违反！超出 {excess_dev*100:.2f} 百分点")
            print(f"📊 指数惩罚: factor={params.h2_penalty_factor}, 惩罚倍数≈{penalty_value:.2f}")
        
        print(f"\n弃电率约束检查:")
        # 检查弃电率是否超限
        if result['curtailment_ratio'] <= params.max_curtailment_ratio:
            print(f"✅ 弃电率约束满足")
        else:
            print(f"⚠️  弃电率超限！")
        
        # 保存结果到output文件夹
        print(f"\n=== 保存结果 ===")
        result_id = save_result_to_output(result, params)
        
        print(f"   • 获取此结果: GET /api/results/{result_id}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_loading():
    """测试数据加载（复用原有逻辑）"""
    
    try:
        from utils.data_loader import load_pv_wind_data
        
        pv_curve, wind_curve = load_pv_wind_data()
        
        if len(pv_curve) == 8760 and len(wind_curve) == 8760:
            print("✅ 数据加载成功")
            return True
        else:
            print("❌ 数据长度不正确")
            return False
            
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False


def test_backend_api():
    """测试后端API连接（复用原有逻辑）"""
    
    try:
        import requests
        
        # 测试API连接
        response = requests.get("http://localhost:8001/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API服务正常")
            
            # 测试获取电解槽类型
            response = requests.get("http://localhost:8001/api/electrolyzer-types", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 可用电解槽类型: {len(data['electrolyzer_types'])}种")
                return True
            else:
                print("❌ 无法获取电解槽类型")
                return False
        else:
            print(f"❌ 后端API响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️  后端API服务未启动，请先运行 python start_backend.py")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False


def compare_with_original():
    """与原版PSO算法进行对比（可选功能）"""
    
    print("\n=== 算法对比建议 ===")
    print("📊 建议同时运行原版测试进行对比:")
    print("   • python test_optimization.py")
    print("   • python test_optimization_v2.py")
    print("\n📈 对比指标:")
    print("   • LCOH值差异")
    print("   • 计算时间对比")
    print("   • 缓存命中率")
    print("   • 收敛稳定性")


def main():
    """主测试函数"""
    
    print("=" * 60)
    print("🚀 优化版PSO算法测试 - 0.1MW步长 + 缓存机制")
    print("=" * 60)
    
    # 测试数据加载
    if not test_data_loading():
        print("❌ 数据加载测试失败，请检查pv.txt和wind.txt文件")
        return
    
    print()
    
    # 测试后端API（可选）
    api_available = test_backend_api()
    if api_available:
        print("✅ 后端API可用，系统完整")
    else:
        print("⚠️  后端API不可用，但可以继续测试优化算法")
    
    print()
    
    # 测试优化版PSO
    if not test_optimized_pso():
        print("❌ 优化版PSO测试失败")
        return
    
    # 显示对比建议
    compare_with_original()
    
    print(f"\n🎉 优化版PSO测试完成！期待看到性能提升。")


if __name__ == "__main__":
    main()
