"""
PSO粒子群优化算法模块
"""

import numpy as np
import random
import time
from config.electrolyzer_specs import ELECTROLYZER_SPECS
from models.parameters import OptimizationParameters
from algorithms.ems import simulate_8760_hours
from algorithms.economics import calculate_lcoh
from utils.data_loader import load_pv_wind_data

def check_electrolyzer_capacity_constraint(particle, selected_types, params: OptimizationParameters):
    """
    检查电解槽总装机容量约束
    
    Args:
        particle: 粒子
        selected_types: 选择的电解槽类型
        params: 优化参数
    
    Returns:
        tuple: (是否满足约束, 总容量)
    """
    elec_counts = particle[3:]  # 电解槽数量部分
    
    total_capacity = 0
    for i, elec_type in enumerate(selected_types):
        elec_spec = ELECTROLYZER_SPECS[elec_type]
        total_capacity += elec_counts[i] * elec_spec['rated_power']
    
    # 约束检查
    if total_capacity < params.electrolyzer_capacity_min or \
       total_capacity > params.electrolyzer_capacity_max:
        return False, total_capacity
    
    return True, total_capacity

def check_annual_grid_constraints(annual_stats, params: OptimizationParameters):
    """
    检查电网年累计约束（包含弃电率硬约束）
    
    Args:
        annual_stats: 年度统计数据
        params: 优化参数
    
    Returns:
        tuple: (是否满足约束, 消息)
    """
    total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
    
    if total_renewable == 0:
        return True, "无新能源发电"
    
    # 上网比例约束
    grid_up_ratio = annual_stats['total_grid_up'] / total_renewable
    if grid_up_ratio > params.max_grid_up_ratio:
        return False, f"上网比例超限: {grid_up_ratio:.2%} > {params.max_grid_up_ratio:.2%}"
    
    # 下网比例约束  
    grid_down_ratio = annual_stats['total_grid_down'] / total_renewable
    if grid_down_ratio > params.max_grid_down_ratio:
        return False, f"下网比例超限: {grid_down_ratio:.2%} > {params.max_grid_down_ratio:.2%}"
    
    # 弃电率硬约束
    curtailment_ratio = annual_stats['total_curtailed'] / total_renewable
    if curtailment_ratio > params.max_curtailment_ratio:
        return False, f"弃电率超限: {curtailment_ratio:.2%} > {params.max_curtailment_ratio:.2%}"
    
    return True, "电网约束满足"

def check_storage_ratio_constraint(particle, params: OptimizationParameters):
    """
    检查储能占新能源容量的最小比例约束
    
    Args:
        particle: 粒子 [pv_capacity, wind_capacity, storage_capacity, ...]
        params: 优化参数
    
    Returns:
        tuple: (是否满足约束, 消息)
    """
    pv_capacity, wind_capacity, storage_capacity = particle[:3]
    
    # 计算新能源总容量
    renewable_capacity = pv_capacity + wind_capacity
    
    if renewable_capacity == 0:
        return True, "无新能源装机"
    
    # 计算储能实际比例
    actual_ratio = storage_capacity / renewable_capacity
    
    # 检查是否满足最小比例要求
    if actual_ratio < params.storage_min_ratio:
        return False, f"储能比例不足: {actual_ratio:.2%} < {params.storage_min_ratio:.2%}"
    
    return True, "储能比例约束满足"

def objective_function(particle, selected_types, pv_curve, wind_curve, params: OptimizationParameters):
    """
    目标函数 - 最小化LCOH + 惩罚项
    
    Args:
        particle: 粒子
        selected_types: 选择的电解槽类型
        pv_curve: 光伏出力曲线
        wind_curve: 风机出力曲线
        params: 优化参数
    
    Returns:
        float: 目标函数值 (LCOH + 惩罚项)
    """
    
    try:
        # 1. 检查电解槽总容量约束
        is_valid, total_elec_capacity = check_electrolyzer_capacity_constraint(particle, selected_types, params)
        if not is_valid:
            return params.constraint_penalty
        
        # 2. 检查储能比例约束
        storage_valid, storage_msg = check_storage_ratio_constraint(particle, params)
        if not storage_valid:
            return params.constraint_penalty
        
        # 3. 8760小时仿真
        hourly_results, annual_stats = simulate_8760_hours(particle, selected_types, pv_curve, wind_curve, params)
        
        # 4. 检查电网年累计约束
        grid_valid, grid_msg = check_annual_grid_constraints(annual_stats, params)
        if not grid_valid:
            return params.constraint_penalty
        
        # 5. 计算LCOH
        lcoh = calculate_lcoh(particle, selected_types, annual_stats, params)
        
        # 6. 产氢量指数惩罚项（弃电率已改为硬约束）
        actual_h2 = annual_stats['total_h2_production']
        h2_penalty = calculate_h2_exponential_penalty(
            actual_h2, 
            params.target_h2_production, 
            lcoh,  # 传递基础LCOH值
            params  # 传递完整参数对象
        )
        
        total_objective = lcoh + h2_penalty
        
        # 防止数值问题
        if np.isnan(total_objective) or np.isinf(total_objective):
            return params.constraint_penalty
        
        return total_objective
        
    except Exception as e:
        print(f"目标函数计算错误: {e}")
        return params.constraint_penalty

def calculate_h2_exponential_penalty(actual_h2, target_h2, base_lcoh, params):
    """
    计算产氢量指数惩罚项
    
    Args:
        actual_h2: 实际产氢量 (Nm³/年)
        target_h2: 目标产氢量 (吨/年)
        base_lcoh: 基础LCOH值 (元/Nm³)
        params: 优化参数
    
    Returns:
        float: 指数惩罚值 (LCOH增量，元/Nm³)
    """
    import math
    
    # 将实际产氢量从Nm³转换为吨 (标准状态下，1 Nm³ 氢气约0.089 kg = 0.000089 吨)
    h2_density = 0.000089  # 吨/Nm³
    actual_h2_tons = actual_h2 * h2_density
    
    if target_h2 == 0:
        return 0.0
    
    deviation_rate = abs(actual_h2_tons - target_h2) / target_h2
    
    if deviation_rate <= params.h2_production_tolerance:
        return 0.0
    else:
        # 指数惩罚公式：惩罚因子 × (e^超出偏差率 - 1) × 基础LCOH
        excess_deviation = deviation_rate - params.h2_production_tolerance
        penalty = params.h2_penalty_factor * (math.exp(excess_deviation) - 1) * base_lcoh
        return penalty

def build_dynamic_bounds(selected_types, params: OptimizationParameters):
    """
    构建动态边界约束
    
    Args:
        selected_types: 选择的电解槽类型
        params: 优化参数
    
    Returns:
        tuple: (bounds, elec_bounds_info)
    """
    
    # 基础边界：光伏、风机、储能
    bounds = [
        (params.pv_capacity_min, params.pv_capacity_max),
        (params.wind_capacity_min, params.wind_capacity_max),
        (params.storage_capacity_min, params.storage_capacity_max),
    ]
    
    # 电解槽数量边界
    elec_bounds_info = []
    for elec_type in selected_types:
        elec_spec = ELECTROLYZER_SPECS[elec_type]
        rated_power = elec_spec['rated_power']  # MW
        
        # 最小数量：保守估计
        min_count = 0
        
        # 最大数量：用最大容量约束来估算
        max_count = int(params.electrolyzer_capacity_max / rated_power) + 5
        
        bounds.append((min_count, max_count))
        elec_bounds_info.append({
            'type': elec_type,
            'rated_power': rated_power,
            'min_count': min_count,
            'max_count': max_count
        })
    
    return bounds, elec_bounds_info

class DynamicPSO:
    """动态维度PSO算法"""
    
    def __init__(self, selected_types, params: OptimizationParameters):
        self.selected_types = selected_types
        self.params = params
        
        # 构建动态边界
        self.bounds, self.elec_bounds_info = build_dynamic_bounds(selected_types, params)
        self.particle_size = len(self.bounds)  # 动态维度大小
        
        # 加载数据
        self.pv_curve, self.wind_curve = load_pv_wind_data()
        
        # PSO参数
        self.swarm_size = params.swarm_size
        self.max_iterations = params.max_iterations
        self.w_start = params.w_start
        self.w_end = params.w_end
        self.c1 = params.c1
        self.c2 = params.c2
        self.convergence_threshold = params.convergence_threshold
        
        # 初始化粒子群
        self.swarm = []
        self.velocities = []
        self.personal_best_positions = []
        self.personal_best_scores = []
        self.global_best_position = None
        self.global_best_score = float('inf')
        
        # 记录历史
        self.best_scores_history = []
        self.convergence_count = 0
    
    def initialize_swarm(self):
        """初始化粒子群"""
        self.swarm = []
        self.velocities = []
        self.personal_best_positions = []
        self.personal_best_scores = []
        
        for _ in range(self.swarm_size):
            # 随机初始化粒子位置
            particle = []
            velocity = []
            
            for i, (low, high) in enumerate(self.bounds):
                if i < 3:  # 前3维是连续变量
                    pos = random.uniform(low, high)
                    vel = random.uniform(-abs(high-low)*0.1, abs(high-low)*0.1)
                else:  # 后面是整数变量
                    pos = random.randint(low, high)
                    vel = random.uniform(-5, 5)
                
                particle.append(pos)
                velocity.append(vel)
            
            self.swarm.append(particle)
            self.velocities.append(velocity)
            
            # 初始化个体最优
            self.personal_best_positions.append(particle[:])
            self.personal_best_scores.append(float('inf'))
    
    def evaluate_fitness(self, particle):
        """评估粒子适应度"""
        return objective_function(particle, self.selected_types, self.pv_curve, self.wind_curve, self.params)
    
    def update_particle(self, i, iteration):
        """更新粒子位置和速度"""
        
        # 线性递减惯性权重
        w = self.w_start - (self.w_start - self.w_end) * iteration / self.max_iterations
        
        for j in range(self.particle_size):
            # 更新速度
            r1 = random.random()
            r2 = random.random()
            
            cognitive_component = self.c1 * r1 * (self.personal_best_positions[i][j] - self.swarm[i][j])
            social_component = self.c2 * r2 * (self.global_best_position[j] - self.swarm[i][j])
            
            self.velocities[i][j] = w * self.velocities[i][j] + cognitive_component + social_component
            
            # 更新位置
            self.swarm[i][j] += self.velocities[i][j]
            
            # 边界处理
            low, high = self.bounds[j]
            if j < 3:  # 连续变量
                self.swarm[i][j] = max(low, min(high, self.swarm[i][j]))
            else:  # 整数变量
                self.swarm[i][j] = int(max(low, min(high, round(self.swarm[i][j]))))
    
    def optimize(self):
        """执行PSO优化"""
        
        start_time = time.time()
        
        # 初始化粒子群
        self.initialize_swarm()
        
        print(f"开始PSO优化，粒子群规模: {self.swarm_size}, 最大迭代次数: {self.max_iterations}")
        print(f"粒子维度: {self.particle_size} (选择了{len(self.selected_types)}种电解槽)")
        
        for iteration in range(self.max_iterations):
            # 评估所有粒子
            current_scores = []
            for i in range(self.swarm_size):
                score = self.evaluate_fitness(self.swarm[i])
                current_scores.append(score)
                
                # 更新个体最优
                if score < self.personal_best_scores[i]:
                    self.personal_best_scores[i] = score
                    self.personal_best_positions[i] = self.swarm[i][:]
                
                # 更新全局最优
                if score < self.global_best_score:
                    self.global_best_score = score
                    self.global_best_position = self.swarm[i][:]
            
            # 记录历史
            self.best_scores_history.append(self.global_best_score)
            
            # 输出进度 - 按照用户要求的格式
            if iteration % 10 == 0 or iteration == self.max_iterations - 1:
                # 获取当前最优解的配置信息
                best_particle = self.global_best_position
                pv_cap = best_particle[0]
                wind_cap = best_particle[1] 
                storage_cap = best_particle[2]
                elec_counts = best_particle[3:]
                
                # 计算电解槽总容量
                total_elec_cap = 0
                for i, elec_type in enumerate(self.selected_types):
                    elec_spec = ELECTROLYZER_SPECS[elec_type]
                    total_elec_cap += elec_counts[i] * elec_spec['rated_power']
                
                # 获取弃电率信息 - 需要快速仿真获取
                try:
                    _, annual_stats = simulate_8760_hours(
                        best_particle, self.selected_types, 
                        self.pv_curve, self.wind_curve, self.params
                    )
                    total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
                    curtailment_ratio = annual_stats['total_curtailed'] / total_renewable if total_renewable > 0 else 0
                    curtailment_percent = curtailment_ratio * 100
                except:
                    curtailment_percent = 0.0  # 如果仿真失败则显示0
                
                print(f"迭代 {iteration:3d}: 最优LCOH={self.global_best_score:.6f}, pv={pv_cap:.0f}, wind={wind_cap:.0f}, bat={storage_cap:.0f}，alk={total_elec_cap:.0f}MW，弃电={curtailment_percent:.1f}%")
            
            # 收敛判断
            if iteration > 10:
                recent_improvement = self.best_scores_history[-10] - self.global_best_score
                if recent_improvement < self.convergence_threshold:
                    self.convergence_count += 1
                    if self.convergence_count >= 5:
                        print(f"第{iteration}代收敛")
                        break
                else:
                    self.convergence_count = 0
            
            # 更新所有粒子
            for i in range(self.swarm_size):
                self.update_particle(i, iteration)
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        # 计算最终结果
        final_results = self.calculate_final_results()
        final_results['calculation_time'] = calculation_time
        final_results['optimization_iterations'] = iteration + 1
        final_results['convergence_achieved'] = self.convergence_count >= 5
        
        print(f"优化完成，耗时: {calculation_time:.2f}秒")
        
        # 获取最优解的配置信息用于简洁输出
        best_particle = self.global_best_position
        pv_cap = best_particle[0]
        wind_cap = best_particle[1] 
        storage_cap = best_particle[2]
        elec_counts = best_particle[3:]
        
        # 计算电解槽总容量
        total_elec_cap = 0
        for i, elec_type in enumerate(self.selected_types):
            elec_spec = ELECTROLYZER_SPECS[elec_type]
            total_elec_cap += elec_counts[i] * elec_spec['rated_power']
        
        # 获取弃电率
        curtailment_percent = final_results['curtailment_ratio'] * 100
        
        print(f"最优LCOH: {self.global_best_score:.6f} 元/Nm³, 光伏={pv_cap:.0f}MW，风机={wind_cap:.0f}MW，储能={storage_cap:.0f}MWh，电解槽={total_elec_cap:.0f}MW，弃电率={curtailment_percent:.1f}%")
        
        # 输出详细成本分解 - 适当换行
        print(f"\n=== 最优解详细成本分解 ===")
        print(f"✓ 投资成本: {final_results['total_capex']/1e8:.2f} 亿元")
        print(f"✓ 首年基础运维: {final_results['basic_opex']/1e4:.1f} 万元/年")
        print(f"✓ 首年制氢水费: {final_results['water_cost']/1e4:.1f} 万元/年")
        print(f"✓ 首年购电成本: {final_results['grid_purchase_cost']/1e4:.1f} 万元/年")
        print(f"✓ 首年总运维成本: {final_results['first_year_opex_total']/1e4:.1f} 万元/年")
        print(f"✓ 总折现运维成本: {final_results['total_discounted_opex']/1e8:.2f} 亿元")
        print(f"✓ 首年制氢量: {final_results['first_year_h2_tons']:.0f} 吨")
        print(f"✓ 年均制氢量: {final_results['annual_h2_production']*0.000089:.0f} 吨 (Nm³)")
        
        return final_results
    
    def calculate_final_results(self):
        """计算最终结果"""
        
        best_particle = self.global_best_position
        
        # 重新仿真获取详细结果
        hourly_results, annual_stats = simulate_8760_hours(
            best_particle, self.selected_types, self.pv_curve, self.wind_curve, self.params
        )
        
        # 解析最优解
        pv_capacity, wind_capacity, storage_capacity = best_particle[:3]
        elec_counts = best_particle[3:]
        
        # 构建电解槽配置
        electrolyzer_config = []
        for i, elec_type in enumerate(self.selected_types):
            elec_spec = ELECTROLYZER_SPECS[elec_type]
            electrolyzer_config.append({
                'type': elec_type,
                'count': int(elec_counts[i]),
                'rated_power': elec_spec['rated_power'],
                'total_capacity': int(elec_counts[i]) * elec_spec['rated_power']
            })
        
        # 计算比例
        total_renewable = annual_stats['total_pv_generation'] + annual_stats['total_wind_generation']
        grid_up_ratio = annual_stats['total_grid_up'] / total_renewable if total_renewable > 0 else 0
        grid_down_ratio = annual_stats['total_grid_down'] / total_renewable if total_renewable > 0 else 0
        curtailment_ratio = annual_stats['total_curtailed'] / total_renewable if total_renewable > 0 else 0
        
        # 计算经济指标和详细成本分解
        from algorithms.economics import calculate_total_capex, calculate_annual_opex
        total_capex = calculate_total_capex(best_particle, self.selected_types, self.params)
        basic_opex = calculate_annual_opex(best_particle, self.selected_types, self.params)
        
        # 详细成本分解
        first_year_h2 = annual_stats['total_h2_production']
        water_cost = first_year_h2 * (self.params.water_consumption / 1000) * self.params.water_price
        grid_purchase_cost = annual_stats['total_grid_down'] * self.params.grid_purchase_price * 1000
        first_year_opex_total = basic_opex + water_cost + grid_purchase_cost
        
        # 折现的总运维成本
        total_discounted_opex = 0
        for year in range(1, self.params.project_years + 1):
            yearly_opex = first_year_opex_total
            discounted_opex = yearly_opex / ((1 + self.params.discount_rate) ** (year - 1))
            total_discounted_opex += discounted_opex
        
        annual_income = annual_stats['total_grid_up'] * self.params.green_electricity_price
        
        # 计算首年制氢量(吨)
        first_year_h2_tons = first_year_h2 * 0.000089  # Nm³转吨
        
        return {
            'optimal_pv_capacity': pv_capacity,
            'optimal_wind_capacity': wind_capacity,
            'optimal_storage_capacity': storage_capacity,
            'optimal_electrolyzer_config': electrolyzer_config,
            'optimal_lcoh': self.global_best_score,
            
            'annual_h2_production': annual_stats['total_h2_production'],
            'annual_pv_generation': annual_stats['total_pv_generation'],
            'annual_wind_generation': annual_stats['total_wind_generation'],
            'annual_grid_up': annual_stats['total_grid_up'],
            'annual_grid_down': annual_stats['total_grid_down'],
            'annual_curtailed': annual_stats['total_curtailed'],
            'grid_up_ratio': grid_up_ratio,
            'grid_down_ratio': grid_down_ratio,
            'curtailment_ratio': curtailment_ratio,
            
            # 小时数统计
            'pv_generation_hours': annual_stats['pv_generation_hours'],
            'wind_generation_hours': annual_stats['wind_generation_hours'],
            'renewable_generation_hours': annual_stats['renewable_generation_hours'],
            'electrolyzer_running_hours': annual_stats['electrolyzer_running_hours'],
            'storage_charge_hours': annual_stats['storage_charge_hours'],
            'storage_discharge_hours': annual_stats['storage_discharge_hours'],
            'grid_up_hours': annual_stats['grid_up_hours'],
            'grid_down_hours': annual_stats['grid_down_hours'],
            
            # 详细成本分解
            'total_capex': total_capex,
            'basic_opex': basic_opex,
            'water_cost': water_cost,
            'grid_purchase_cost': grid_purchase_cost,
            'first_year_opex_total': first_year_opex_total,
            'total_discounted_opex': total_discounted_opex,
            'first_year_h2_tons': first_year_h2_tons,
            'annual_income': annual_income,
            
            'hourly_data': hourly_results
        }