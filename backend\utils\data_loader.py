"""
数据加载工具
"""

import numpy as np
import os
from typing import <PERSON>ple

def load_pv_wind_data() -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
    """
    加载光伏和风机出力曲线数据
    
    Returns:
        Tuple[np.ndarray, np.ndarray]: (pv_curve, wind_curve) 8760小时标幺值
    """
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    pv_file = os.path.join(project_root, 'pv.txt')
    wind_file = os.path.join(project_root, 'wind.txt')
    
    # 加载光伏数据
    try:
        with open(pv_file, 'r') as f:
            pv_data = [float(line.strip()) for line in f.readlines() if line.strip()]
        pv_curve = np.array(pv_data)
    except Exception as e:
        print(f"加载光伏数据失败: {e}")
        # 使用示例数据
        pv_curve = generate_sample_pv_curve()
    
    # 加载风机数据
    try:
        with open(wind_file, 'r') as f:
            wind_data = []
            for line in f.readlines():
                line = line.strip()
                if line:
                    # 处理可能存在的特殊字符，如#号
                    if '#' in line:
                        line = line.replace('#', '')
                    wind_data.append(float(line))
        wind_curve = np.array(wind_data)
    except Exception as e:
        print(f"加载风机数据失败: {e}")
        # 使用示例数据
        wind_curve = generate_sample_wind_curve()
    
    # 确保数据长度为8760
    if len(pv_curve) != 8760:
        print(f"警告: 光伏数据长度为{len(pv_curve)}，预期8760")
        if len(pv_curve) > 8760:
            pv_curve = pv_curve[:8760]
        else:
            # 重复数据至8760小时
            repeat_times = 8760 // len(pv_curve) + 1
            pv_curve = np.tile(pv_curve, repeat_times)[:8760]
    
    if len(wind_curve) != 8760:
        print(f"警告: 风机数据长度为{len(wind_curve)}，预期8760")
        if len(wind_curve) > 8760:
            wind_curve = wind_curve[:8760]
        else:
            # 重复数据至8760小时
            repeat_times = 8760 // len(wind_curve) + 1
            wind_curve = np.tile(wind_curve, repeat_times)[:8760]
    
    return pv_curve, wind_curve

def generate_sample_pv_curve() -> np.ndarray:
    """生成示例光伏出力曲线"""
    curve = []
    for day in range(365):
        for hour in range(24):
            if 6 <= hour <= 18:
                # 白天有出力，使用正弦曲线模拟
                angle = (hour - 6) * np.pi / 12
                power = max(0, np.sin(angle) * 0.8)
            else:
                power = 0
            curve.append(power)
    return np.array(curve)

def generate_sample_wind_curve() -> np.ndarray:
    """生成示例风机出力曲线"""
    np.random.seed(42)  # 固定随机种子
    # 生成带有一定规律的随机风机出力
    base_curve = np.random.beta(2, 5, 8760) * 0.9  # beta分布模拟风力特性
    # 添加日周期性
    daily_pattern = np.tile(np.sin(np.linspace(0, 2*np.pi, 24)), 365)
    wind_curve = base_curve + 0.1 * daily_pattern
    wind_curve = np.clip(wind_curve, 0, 1)  # 限制在0-1之间
    return wind_curve 