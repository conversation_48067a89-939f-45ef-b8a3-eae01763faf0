"""
输入参数模型定义
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum

class ElectrolyzerType(str, Enum):
    HI1_PLUS_1000 = "Hi1_PLus_1000"
    G_2000 = "G_2000"
    G_3000 = "G_3000"
    P_200 = "P_200"

class OptimizationParameters(BaseModel):
    """优化计算输入参数"""
    
    # 光伏设备参数
    pv_capacity_min: float = Field(default=10, description="光伏装机容量下限(MW)")
    pv_capacity_max: float = Field(default=200, description="光伏装机容量上限(MW)")
    pv_first_year_degradation: float = Field(default=0.01, description="光伏首年衰减率")
    pv_annual_degradation: float = Field(default=0.0035, description="光伏年衰减率")
    pv_epc_cost: float = Field(default=2.8, description="光伏EPC投资(元/W)")
    pv_om_cost: float = Field(default=0.05, description="光伏运维成本(元/W/年)")
    
    # 风机参数
    wind_capacity_min: float = Field(default=10, description="风机装机容量下限(MW)")
    wind_capacity_max: float = Field(default=200, description="风机装机容量上限(MW)")
    wind_epc_cost: float = Field(default=3.2, description="风机EPC投资(元/W)")
    wind_om_cost: float = Field(default=0.6, description="风机运维成本(元/W/年)")
    
    # 电网相关参数
    max_grid_down_ratio: float = Field(default=0.1, description="年最大下网比例")
    max_grid_up_ratio: float = Field(default=0.2, description="年最大上网比例")
    grid_purchase_price: float = Field(default=0.45, description="网购电价(元/kWh)")
    green_electricity_price: float = Field(default=0.332, description="绿电上网价格(元/kWh)")
    
    # 储能相关参数
    storage_capacity_min: float = Field(default=10, description="储能装机容量下限(MWh)")
    storage_capacity_max: float = Field(default=500, description="储能装机容量上限(MWh)")
    storage_min_ratio: float = Field(default=0.0, description="储能占新能源容量的最小比例，硬约束")
    storage_epc_cost: float = Field(default=0.7, description="储能EPC投资(元/Wh)")
    storage_om_cost: float = Field(default=0.04, description="储能运维成本(元/Wh/年)")
    charge_efficiency: float = Field(default=0.95, description="储能充电效率")
    discharge_efficiency: float = Field(default=0.95, description="储能放电效率")
    charge_discharge_rate: float = Field(default=0.5, description="储能充放电倍率")
    initial_soc: float = Field(default=0.5, description="储能初始SOC")
    soc_min: float = Field(default=0.1, description="储能SOC下限")
    soc_max: float = Field(default=0.9, description="储能SOC上限")
    
    # 电解槽相关参数
    selected_electrolyzer_types: List[ElectrolyzerType] = Field(
        default=["Hi1_PLus_1000", "G_2000"], 
        description="选择的电解槽类型(最多3种)",
        min_items=1,
        max_items=3
    )
    electrolyzer_capacity_min: float = Field(default=20, description="电解槽总装机容量下限(MW)")
    electrolyzer_capacity_max: float = Field(default=200, description="电解槽总装机容量上限(MW)")
    water_price: float = Field(default=4.38, description="用水价格(元/吨)")
    water_consumption: float = Field(default=1.4, description="制氢耗水量(L/Nm³)")
    om_ratio: float = Field(default=0.02, description="电解槽运维比例")
    
    # 项目经济参数
    project_years: int = Field(default=25, description="项目周期(年)")
    target_h2_production: float = Field(default=1000, description="目标年产氢量(吨)")
    loan_years: int = Field(default=15, description="贷款年限(年)")
    loan_ratio: float = Field(default=0.7, description="贷款比例")
    loan_rate: float = Field(default=0.031, description="贷款利率")
    discount_rate: float = Field(default=0.06, description="设备折现率")
    
    # PSO算法参数
    swarm_size: int = Field(default=40, description="粒子群规模")
    max_iterations: int = Field(default=150, description="最大迭代次数")
    w_start: float = Field(default=0.9, description="初始惯性权重")
    w_end: float = Field(default=0.4, description="终止惯性权重")
    c1: float = Field(default=2.0, description="认知学习因子")
    c2: float = Field(default=2.0, description="社会学习因子")
    convergence_threshold: float = Field(default=1e-6, description="收敛阈值")
    
    # 约束和惩罚参数
    constraint_penalty: float = Field(default=1e6, description="约束违反惩罚值")
    
    # ==================== 产氢量约束参数 ====================
    h2_production_tolerance: float = Field(default=0.02, description="产氢量偏差容忍度，默认2%")
    h2_penalty_factor: float = Field(default=2.0, description="产氢量指数惩罚强度因子，penalty = factor × (e^excess - 1) × base_lcoh，建议范围1.0-5.0")
    
    # ==================== 弃电率硬约束 ====================
    max_curtailment_ratio: float = Field(default=0.10, description="最大允许弃电率，默认10%，超出将直接拒绝解")

class OptimizationResult(BaseModel):
    """优化计算结果"""
    
    # 优化结果
    optimal_pv_capacity: float
    optimal_wind_capacity: float
    optimal_storage_capacity: float
    optimal_electrolyzer_config: List[dict]
    optimal_lcoh: float
    
    # 年度统计
    annual_h2_production: float
    annual_pv_generation: float
    annual_wind_generation: float
    annual_grid_up: float
    annual_grid_down: float
    annual_curtailed: float
    grid_up_ratio: float
    grid_down_ratio: float
    curtailment_ratio: float
    
    # 经济指标
    total_capex: float
    total_opex: float
    annual_income: float
    
    # 8760小时数据
    hourly_data: List[dict]
    
    # 计算状态
    optimization_iterations: int
    convergence_achieved: bool
    calculation_time: float 