"""
电解槽设备规格配置
"""

ELECTROLYZER_SPECS = {
    'Hi1_PLus_1000': {
        'name': 'Hi1_PLus_1000',
        'capacity': 1000,           # Nm³/h
        'power_consumption': 4.6,   # kWh/Nm³
        'rated_power': 5,           # MW
        'min_load_rate': 0.3,
        'max_load_rate': 1.1,
        'auxiliary_consumption': 0.3, # kWh/Nm³
        'epc_price': 18000000        # 元/套 (3600元/kW)
    },
    'G_2000': {
        'name': 'G_2000',
        'capacity': 2000,
        'power_consumption': 4.3,
        'rated_power': 10,          # MW
        'min_load_rate': 0.3,
        'max_load_rate': 1.1,
        'auxiliary_consumption': 0.3,
        'epc_price': 35000000        # 元/套 (3500元/kW)
    },
    'G_3000': {
        'name': 'G_3000',
        'capacity': 3000,
        'power_consumption': 4.3,
        'rated_power': 15,          # MW
        'min_load_rate': 0.3,
        'max_load_rate': 1.1,
        'auxiliary_consumption': 0.3,
        'epc_price': 52500000        # 元/套 (3500元/kW)
    },
    'P_200': {
        'name': 'P_200',
        'capacity': 200,
        'power_consumption': 4.3,
        'rated_power': 1,           # MW
        'min_load_rate': 0,         # P_200最低负载率为0
        'max_load_rate': 1.1,
        'auxiliary_consumption': 0.3,
        'epc_price': 4000000        # 元/套 (4000元/kW)
    }
}

def get_consumption_factor(elec_type, load_rate):
    """
    根据电解槽类型和负载率计算能耗系数
    
    Args:
        elec_type: 电解槽类型
        load_rate: 负载率 (0.0-1.1)
    
    Returns:
        float: 能耗系数 (实际能耗/设计能耗)
    """
    # 确保负载率在有效范围内
    load_rate = max(0.0, min(1.1, load_rate))
    
    if elec_type in ['Hi1_PLus_1000', 'G_2000', 'G_3000']:
        # 碱性电解槽能耗曲线: U型曲线，最优点在90%负载
        # 30%负载时能耗增加20%，100%负载时为设计能耗，110%负载时能耗增加8%
        factor = 0.15 * load_rate**2 - 0.25 * load_rate + 1.20
    elif elec_type == 'P_200':
        # PEM电解槽能耗曲线: 较平缓的U型曲线，最优点在95%负载  
        # 30%负载时能耗增加12%，100%负载时为设计能耗，110%负载时能耗增加5%
        factor = 0.08 * load_rate**2 - 0.15 * load_rate + 1.12
    else:
        # 未知类型，使用默认值
        factor = 1.0
    
    # 限制系数在合理范围内 (0.8-1.3)
    return max(0.8, min(1.3, factor))

def get_actual_power_consumption(elec_type, load_rate):
    """
    获取考虑负载率影响的实际电耗
    
    Args:
        elec_type: 电解槽类型
        load_rate: 负载率
    
    Returns:
        float: 实际总电耗 (kWh/Nm³)，包含主电耗和辅助电耗
    """
    if elec_type not in ELECTROLYZER_SPECS:
        raise ValueError(f"未知的电解槽类型: {elec_type}")
    
    elec_spec = ELECTROLYZER_SPECS[elec_type]
    base_consumption = elec_spec['power_consumption'] + elec_spec['auxiliary_consumption']
    consumption_factor = get_consumption_factor(elec_type, load_rate)
    
    return base_consumption * consumption_factor

def get_electrolyzer_types():
    """获取所有可用的电解槽类型"""
    return list(ELECTROLYZER_SPECS.keys())

def get_electrolyzer_spec(electrolyzer_type):
    """获取指定电解槽规格"""
    return ELECTROLYZER_SPECS.get(electrolyzer_type) 